'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { Template } from '@/types';
import { collection, addDoc, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from '@/lib/toast';

interface TemplateDialogProps {
  open: boolean;
  onClose: () => void;
  template?: Template | null;
  onSuccess: () => void;
}

export default function TemplateDialog({ open, onClose, template, onSuccess }: TemplateDialogProps) {
  const [loading, setLoading] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    shortDescription: '',
    detailedDescription: '',
    category: '',
    price: '',
    originalPrice: '',
    imageUrl: '',
    previewUrl: '',
    demoUrl: '',
    downloadUrl: '',
    featured: false,
    premium: false,
    free: false,
    discount: '',
    version: '1.0.0',
    setupTime: '',
    difficultyLevel: 'Beginner',
    licenseType: 'Standard License',
    keyFeatures: '',
    technologyStack: ''
  });

  useEffect(() => {
    if (template) {
      setFormData({
        title: template.title,
        description: template.description,
        category: template.category,
        price: template.price.toString(),
        originalPrice: template.originalPrice?.toString() || '',
        imageUrl: template.imageUrl,
        previewUrl: template.previewUrl || '',
        downloadUrl: template.downloadUrl || '',
        featured: template.featured,
        discount: template.discount?.toString() || ''
      });
      setTags(template.tags || []);
    } else {
      setFormData({
        title: '',
        description: '',
        category: '',
        price: '',
        originalPrice: '',
        imageUrl: '',
        previewUrl: '',
        downloadUrl: '',
        featured: false,
        discount: ''
      });
      setTags([]);
    }
  }, [template, open]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const templateData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        price: parseFloat(formData.price),
        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,
        imageUrl: formData.imageUrl,
        previewUrl: formData.previewUrl || undefined,
        downloadUrl: formData.downloadUrl || undefined,
        tags: tags,
        featured: formData.featured,
        discount: formData.discount ? parseFloat(formData.discount) : undefined,
        rating: template?.rating || 0,
        downloads: template?.downloads || 0,
        updatedAt: new Date(),
        createdBy: 'admin'
      };

      if (template) {
        // Update existing template
        await updateDoc(doc(db, 'templates', template.id), templateData);
        toast.success('Template updated successfully');
      } else {
        // Add new template
        await addDoc(collection(db, 'templates'), {
          ...templateData,
          createdAt: new Date()
        });
        toast.success('Template added successfully');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {template ? 'Edit Template' : 'Add New Template'}
          </DialogTitle>
          <DialogDescription>
            {template ? 'Update template information' : 'Fill in the details for your new template'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Template Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., SaaS Dashboard Pro"
                required
              />
            </div>
            <div>
              <Label htmlFor="category">Category *</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Technology">Technology</SelectItem>
                  <SelectItem value="Business">Business</SelectItem>
                  <SelectItem value="Education">Education</SelectItem>
                  <SelectItem value="Portfolio">Portfolio</SelectItem>
                  <SelectItem value="E-commerce">E-commerce</SelectItem>
                  <SelectItem value="Dashboard">Dashboard</SelectItem>
                  <SelectItem value="Blog">Blog</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your template..."
              rows={3}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="price">Price (₹) *</Label>
              <Input
                id="price"
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="2499"
                min="0"
                required
              />
            </div>
            <div>
              <Label htmlFor="originalPrice">Original Price (₹)</Label>
              <Input
                id="originalPrice"
                type="number"
                value={formData.originalPrice}
                onChange={(e) => handleInputChange('originalPrice', e.target.value)}
                placeholder="3499"
                min="0"
              />
            </div>
            <div>
              <Label htmlFor="discount">Discount (%)</Label>
              <Input
                id="discount"
                type="number"
                value={formData.discount}
                onChange={(e) => handleInputChange('discount', e.target.value)}
                placeholder="20"
                min="0"
                max="100"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="imageUrl">Image URL *</Label>
            <Input
              id="imageUrl"
              type="url"
              value={formData.imageUrl}
              onChange={(e) => handleInputChange('imageUrl', e.target.value)}
              placeholder="https://images.unsplash.com/..."
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="previewUrl">Preview URL</Label>
              <Input
                id="previewUrl"
                type="url"
                value={formData.previewUrl}
                onChange={(e) => handleInputChange('previewUrl', e.target.value)}
                placeholder="https://demo.example.com"
              />
            </div>
            <div>
              <Label htmlFor="downloadUrl">Download URL</Label>
              <Input
                id="downloadUrl"
                type="url"
                value={formData.downloadUrl}
                onChange={(e) => handleInputChange('downloadUrl', e.target.value)}
                placeholder="https://files.example.com/template.zip"
              />
            </div>
          </div>

          <div>
            <Label>Tags</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={formData.featured}
              onCheckedChange={(checked) => handleInputChange('featured', checked)}
            />
            <Label htmlFor="featured">Featured Template</Label>
          </div>

          <div className="flex items-center justify-between pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : template ? 'Update Template' : 'Add Template'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
