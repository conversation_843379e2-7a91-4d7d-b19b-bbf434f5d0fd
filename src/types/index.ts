export interface Template {
  id: string;
  title: string;
  description: string;
  detailedDescription?: string;
  category: string;
  price: number;
  originalPrice?: number;
  imageUrl: string;
  previewUrl?: string;
  demoUrl?: string;
  downloadUrl?: string;
  tags?: string[];
  featured: boolean;
  premium?: boolean;
  free?: boolean;
  rating?: number;
  downloads?: number;
  discount?: number;
  version?: string;
  setupTime?: string;
  difficultyLevel?: string;
  licenseType?: string;
  keyFeatures?: string;
  technologyStack?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  templateCount: number;
}

export interface Order {
  id: string;
  userId: string;
  templateId: string;
  templateTitle: string;
  amount: number;
  status: 'pending' | 'confirmed' | 'declined' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  paymentMethod?: string;
  notes?: string;
}

export interface CustomRequest {
  id: string;
  userId: string;
  userEmail: string;
  title: string;
  description: string;
  category: string;
  budget?: number;
  deadline?: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  adminNotes?: string;
}

export interface User {
  uid: string;
  email: string;
  role: 'admin' | 'user';
  displayName?: string;
  fullName?: string;
  phoneNumber?: string;
  countryCode?: string;
  createdAt: Date;
  updatedAt?: Date;
  purchasedTemplates?: string[];
}

export interface ContactMessage {
  id: string;
  userId?: string;
  userEmail: string;
  userName: string;
  subject: string;
  message: string;
  type: 'contact' | 'buy-request';
  templateId?: string;
  templateTitle?: string;
  status: 'pending' | 'responded' | 'resolved';
  createdAt: Date;
  updatedAt: Date;
  adminNotes?: string;
}


