import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit, 
  where,
  onSnapshot,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebase';
import { CustomRequest, User, ContactMessage } from '@/types';

// Custom Requests Services
export const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const docRef = await addDoc(collection(db, 'customRequests'), {
      ...requestData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating custom request:', error);
    throw error;
  }
};

export const getCustomRequests = async () => {
  try {
    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomRequest[];
  } catch (error) {
    console.error('Error fetching custom requests:', error);
    throw error;
  }
};

export const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date()
    };
    
    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }
    
    await updateDoc(doc(db, 'customRequests', requestId), updateData);
  } catch (error) {
    console.error('Error updating custom request:', error);
    throw error;
  }
};

// User Management Services
export const getAllUsers = async () => {
  try {
    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as User[];
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const getUserById = async (userId: string) => {
  try {
    const docRef = doc(db, 'users', userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as User;
    } else {
      throw new Error('User not found');
    }
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

// Dashboard Statistics Services
export const getDashboardStats = async () => {
  try {
    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([
      getDocs(collection(db, 'users')),
      getDocs(collection(db, 'templates')),
      getDocs(collection(db, 'customRequests'))
    ]);

    const totalUsers = usersSnapshot.size;
    const totalTemplates = templatesSnapshot.size;
    const totalRequests = requestsSnapshot.size;
    
    // Calculate pending requests
    const pendingRequests = requestsSnapshot.docs.filter(
      doc => doc.data().status === 'pending'
    ).length;

    return {
      totalUsers,
      totalTemplates,
      totalRequests,
      pendingRequests,
      totalSales: 0, // Placeholder for sales data
      customizations: 0 // Placeholder for customizations data
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// Real-time listeners
export const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {
  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));
  
  return onSnapshot(q, (querySnapshot) => {
    const requests = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomRequest[];
    callback(requests);
  });
};

export const subscribeToUsers = (callback: (users: User[]) => void) => {
  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));

  return onSnapshot(q, (querySnapshot) => {
    const users = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as User[];
    callback(users);
  });
};

// Contact Messages Services
export const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const docRef = await addDoc(collection(db, 'contactMessages'), {
      ...messageData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating contact message:', error);
    throw error;
  }
};

export const getContactMessages = async () => {
  try {
    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ContactMessage[];
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    throw error;
  }
};

export const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    await updateDoc(doc(db, 'contactMessages', messageId), updateData);
  } catch (error) {
    console.error('Error updating contact message:', error);
    throw error;
  }
};

export const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {
  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));

  return onSnapshot(q, (querySnapshot) => {
    const messages = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ContactMessage[];
    callback(messages);
  });
};

// Contact Messages Services
export const getContactMessages = async () => {
  try {
    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ContactMessage[];
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    throw error;
  }
};

export const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status']) => {
  try {
    await updateDoc(doc(db, 'contactMessages', messageId), {
      status,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating contact message:', error);
    throw error;
  }
};
