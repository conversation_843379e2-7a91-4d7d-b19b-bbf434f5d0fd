{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/templates/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Search, Eye, IndianRupee, Star, Heart, Download, ExternalLink, Zap, Award, Filter, MessageCircle } from \"lucide-react\"\nimport { collection, getDocs, query, orderBy } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { Template } from '@/types';\nimport { toast } from \"sonner\"\nimport { useRouter, usePathname } from \"next/navigation\"\nimport { useAuth } from '@/contexts/AuthContext';\nimport { createContactMessage } from '@/lib/firebaseServices';\n\nexport default function TemplatesPage() {\n  const [templates, setTemplates] = useState<Template[]>([])\n  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedCategory, setSelectedCategory] = useState(\"All\")\n  const [sortBy, setSortBy] = useState(\"title\")\n  const [categories, setCategories] = useState<string[]>([\"All\"])\n\n  const router = useRouter()\n  const pathname = usePathname()\n  const { user, userData } = useAuth()\n\n  useEffect(() => {\n    const handlePageShow = (event: PageTransitionEvent) => {\n      if (event.persisted) {\n        console.log('Page restored from bfcache, refetching templates...')\n        fetchTemplates()\n      }\n    };\n\n    fetchTemplates()\n    window.addEventListener('pageshow', handlePageShow);\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow);\n    };\n  }, [pathname])\n\n  useEffect(() => {\n    filterAndSortTemplates()\n  }, [templates, searchTerm, selectedCategory, sortBy])\n\n  const fetchTemplates = async () => {\n    try {\n      const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));\n      const querySnapshot = await getDocs(templatesQuery);\n      const templatesData = querySnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data(),\n        createdAt: doc.data().createdAt?.toDate() || new Date(),\n        updatedAt: doc.data().updatedAt?.toDate() || new Date(),\n      })) as Template[];\n\n      setTemplates(templatesData);\n\n      // Extract unique categories\n      const uniqueCategories = [\"All\", ...new Set(templatesData?.map(t => t.category) || [])]\n      setCategories(uniqueCategories)\n    } catch (error) {\n      console.error('Error fetching templates:', error)\n      toast.error('Failed to load templates')\n      \n      // Fallback to mock data\n      const mockTemplates = [\n        {\n          id: '1',\n          title: 'Creative Portfolio',\n          description: 'Showcase your creative work with this stunning portfolio template',\n          category: 'Portfolio',\n          price: 1999,\n          imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n          rating: 4.9,\n          downloads: 1234,\n          featured: true,\n          tags: ['Responsive', 'Modern', 'Fast'],\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        },\n        {\n          id: '2',\n          title: 'E-commerce Store',\n          description: 'Complete e-commerce solution with shopping cart and payment integration',\n          category: 'E-commerce',\n          price: 4999,\n          imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',\n          rating: 4.9,\n          downloads: 1234,\n          featured: true,\n          tags: ['Responsive', 'Modern', 'Fast'],\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        },\n        {\n          id: '3',\n          title: 'Education Platform',\n          description: 'Complete education platform template with course management',\n          category: 'Education',\n          price: 3499,\n          imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',\n          rating: 4.9,\n          downloads: 1234,\n          featured: true,\n          tags: ['Responsive', 'Modern', 'Fast'],\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        }\n      ] as Template[];\n      \n      setTemplates(mockTemplates);\n      setCategories([\"All\", \"Portfolio\", \"E-commerce\", \"Education\"]);\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const filterAndSortTemplates = () => {\n    let filtered = templates\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(template =>\n        template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        template.description?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // Filter by category\n    if (selectedCategory !== \"All\") {\n      filtered = filtered.filter(template => template.category === selectedCategory)\n    }\n\n    // Sort templates\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case \"price-low\":\n          return a.price - b.price\n        case \"price-high\":\n          return b.price - a.price\n        case \"title\":\n        default:\n          return a.title.localeCompare(b.title)\n      }\n    })\n\n    setFilteredTemplates(filtered)\n  }\n\n  const handleContactRequest = async (template: Template) => {\n    if (!user) {\n      toast.error('Please sign in to contact us')\n      router.push('/auth')\n      return\n    }\n\n    // Check if user has completed profile (mobile number required)\n    if (!userData?.phoneNumber || !userData?.fullName) {\n      toast.error('Please complete your profile with mobile number before contacting us')\n      router.push('/profile')\n      return\n    }\n\n    try {\n      await createContactMessage({\n        userId: user.uid,\n        userEmail: user.email!,\n        userName: userData.fullName,\n        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,\n        subject: `Inquiry about ${template.title}`,\n        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information about customization options and pricing?`,\n        type: 'contact',\n        templateId: template.id,\n        templateTitle: template.title,\n        status: 'pending'\n      })\n\n      toast.success('Contact request sent! We\\'ll get back to you soon.')\n    } catch (error) {\n      console.error('Error sending contact request:', error)\n      toast.error('Failed to send contact request. Please try again.')\n    }\n  }\n\n  const handleBuyRequest = async (template: Template) => {\n    if (!user) {\n      toast.error('Please sign in to make a purchase request')\n      router.push('/auth')\n      return\n    }\n\n    // Check if user has completed profile (mobile number required)\n    if (!userData?.phoneNumber || !userData?.fullName) {\n      toast.error('Please complete your profile with mobile number before making a purchase request')\n      router.push('/profile')\n      return\n    }\n\n    try {\n      await createContactMessage({\n        userId: user.uid,\n        userEmail: user.email!,\n        userName: userData.fullName,\n        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,\n        subject: `Purchase Request for ${template.title}`,\n        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment instructions and delivery details.`,\n        type: 'buy-request',\n        templateId: template.id,\n        templateTitle: template.title,\n        status: 'pending'\n      })\n\n      toast.success('Purchase request sent! We\\'ll contact you with payment details.')\n    } catch (error) {\n      console.error('Error sending buy request:', error)\n      toast.error('Failed to send purchase request. Please try again.')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Templates</h1>\n          <p className=\"text-muted-foreground\">Loading templates...</p>\n        </div>\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n          {[...Array(8)].map((_, i) => (\n            <Card key={i} className=\"overflow-hidden\">\n              <div className=\"aspect-video bg-muted animate-pulse\" />\n              <CardHeader>\n                <div className=\"h-4 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 bg-muted animate-pulse rounded w-3/4\" />\n              </CardHeader>\n            </Card>\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\n      {/* Enhanced Header */}\n      <div className=\"text-center space-y-4 py-8\">\n        <div className=\"space-y-2\">\n          <Badge variant=\"secondary\" className=\"mb-4\">\n            <Award className=\"h-4 w-4 mr-2\" />\n            50+ Premium Templates\n          </Badge>\n          <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n            Premium Templates\n          </h1>\n          <p className=\"text-base sm:text-lg lg:text-xl text-muted-foreground max-w-2xl mx-auto\">\n            Choose from our collection of professionally designed, responsive templates.\n            Each template is crafted with modern design principles and best practices.\n          </p>\n        </div>\n\n        {/* Stats */}\n        <div className=\"flex justify-center items-center gap-6 sm:gap-8 pt-4\">\n          <div className=\"text-center\">\n            <div className=\"text-xl sm:text-2xl font-bold text-blue-600\">50+</div>\n            <div className=\"text-xs sm:text-sm text-muted-foreground\">Templates</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-xl sm:text-2xl font-bold text-green-600\">4.9★</div>\n            <div className=\"text-xs sm:text-sm text-muted-foreground\">Rating</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-xl sm:text-2xl font-bold text-purple-600\">10K+</div>\n            <div className=\"text-xs sm:text-sm text-muted-foreground\">Downloads</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Search and Filter */}\n      <div className=\"bg-muted/30 rounded-2xl p-4 sm:p-6 space-y-4\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search templates by name or description...\"\n              className=\"pl-10 h-10 sm:h-12 text-sm sm:text-lg\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          <Select value={sortBy} onValueChange={setSortBy}>\n            <SelectTrigger className=\"w-full lg:w-48 h-10 sm:h-12\">\n              <SelectValue placeholder=\"Sort by\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"title\">Sort by Title</SelectItem>\n              <SelectItem value=\"price-low\">Price: Low to High</SelectItem>\n              <SelectItem value=\"price-high\">Price: High to Low</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"space-y-2\">\n          <h3 className=\"text-sm font-medium text-muted-foreground\">Categories</h3>\n          <div className=\"flex gap-2 flex-wrap\">\n            {categories.map((category) => (\n              <Button\n                key={category}\n                variant={category === selectedCategory ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category)}\n                className=\"rounded-full text-xs sm:text-sm\"\n              >\n                {category}\n                {category !== \"All\" && (\n                  <Badge variant=\"secondary\" className=\"ml-2 text-xs\">\n                    {templates.filter(t => t.category === category).length}\n                  </Badge>\n                )}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\n          <span>\n            Showing {filteredTemplates.length} of {templates.length} templates\n          </span>\n          {searchTerm && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSearchTerm(\"\")}\n              className=\"text-xs\"\n            >\n              Clear search\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Enhanced Templates Grid */}\n      <div className=\"grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n        {filteredTemplates.map((template) => (\n          <Card key={template.id} className=\"group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg\">\n            {/* Image Container with Overlay */}\n            <div className=\"aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden\">\n              {template.imageUrl ? (\n                <img\n                  src={template.imageUrl}\n                  alt={template.title}\n                  className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n              ) : (\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-center space-y-2\">\n                    <div className=\"w-12 sm:w-16 h-12 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto\">\n                      <Zap className=\"h-6 w-6 sm:h-8 sm:w-8 text-blue-600\" />\n                    </div>\n                    <p className=\"text-muted-foreground font-medium text-sm sm:text-base\">Template Preview</p>\n                  </div>\n                </div>\n              )}\n\n              {/* Overlay with Quick Actions */}\n              <div className=\"absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2 sm:gap-3\">\n                <Button\n                  size=\"sm\"\n                  variant=\"secondary\"\n                  className=\"bg-white/90 hover:bg-white text-black border-0 text-xs sm:text-sm\"\n                  onClick={() => window.open('/customize', '_blank')}\n                >\n                  <Eye className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2\" />\n                  Preview\n                </Button>\n                <Button\n                  size=\"sm\"\n                  variant=\"secondary\"\n                  className=\"bg-white/90 hover:bg-white text-black border-0 text-xs sm:text-sm\"\n                  onClick={() => router.push('/customize')}\n                >\n                  <ExternalLink className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2\" />\n                  Customize\n                </Button>\n              </div>\n\n              {/* Category Badge */}\n              <div className=\"absolute top-2 sm:top-3 left-2 sm:left-3\">\n                <Badge variant=\"secondary\" className=\"bg-white/90 text-black border-0 text-xs\">\n                  {template.category}\n                </Badge>\n              </div>\n\n              {/* Favorite Button */}\n              <div className=\"absolute top-2 sm:top-3 right-2 sm:right-3\">\n                <Button\n                  size=\"sm\"\n                  variant=\"secondary\"\n                  className=\"w-6 h-6 sm:w-8 sm:h-8 p-0 bg-white/90 hover:bg-white text-black border-0 rounded-full\"\n                >\n                  <Heart className=\"h-3 w-3 sm:h-4 sm:w-4\" />\n                </Button>\n              </div>\n            </div>\n\n            {/* Card Content */}\n            <CardHeader className=\"pb-3\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-start justify-between\">\n                  <CardTitle className=\"text-base sm:text-xl font-bold group-hover:text-blue-600 transition-colors line-clamp-2\">\n                    {template.title}\n                  </CardTitle>\n                  <div className=\"flex items-center gap-1 text-yellow-500 flex-shrink-0 ml-2\">\n                    <Star className=\"h-3 w-3 sm:h-4 sm:w-4 fill-current\" />\n                    <span className=\"text-xs sm:text-sm font-medium\">{template.rating || '4.9'}</span>\n                  </div>\n                </div>\n                <CardDescription className=\"text-xs sm:text-sm leading-relaxed line-clamp-2\">\n                  {template.description}\n                </CardDescription>\n              </div>\n            </CardHeader>\n\n            <CardContent className=\"pt-0\">\n              {/* Features */}\n              <div className=\"flex flex-wrap gap-1 mb-3 sm:mb-4\">\n                <Badge variant=\"outline\" className=\"text-xs\">Responsive</Badge>\n                <Badge variant=\"outline\" className=\"text-xs\">Modern</Badge>\n                <Badge variant=\"outline\" className=\"text-xs\">Fast</Badge>\n              </div>\n\n              {/* Price and Actions */}\n              <div className=\"space-y-3 sm:space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-1\">\n                    <IndianRupee className=\"h-4 w-4 sm:h-5 sm:w-5 text-green-600\" />\n                    <span className=\"text-2xl sm:text-3xl font-bold text-green-600\">₹{template.price}</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-xs text-muted-foreground line-through\">₹{Math.round(template.price * 1.5)}</div>\n                    <div className=\"text-xs text-green-600 font-medium\">33% OFF</div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"space-y-2\">\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"w-full group/btn hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all text-xs sm:text-sm\"\n                      onClick={() => window.open('/customize', '_blank')}\n                    >\n                      <Eye className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                      <span className=\"hidden sm:inline\">Preview</span>\n                      <span className=\"sm:hidden\">View</span>\n                    </Button>\n\n                    <Button\n                      className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn text-xs sm:text-sm\"\n                      onClick={() => handleBuyRequest(template)}\n                    >\n                      <MessageCircle className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                      <span className=\"hidden sm:inline\">Contact to Buy</span>\n                      <span className=\"sm:hidden\">Buy</span>\n                    </Button>\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    className=\"w-full group/btn hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-all text-xs sm:text-sm\"\n                    onClick={() => handleContactRequest(template)}\n                  >\n                    <MessageCircle className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform\" />\n                    <span className=\"hidden sm:inline\">Contact for Info</span>\n                    <span className=\"sm:hidden\">Contact</span>\n                  </Button>\n                </div>\n\n                {/* Quick Info */}\n                <div className=\"flex items-center justify-between text-xs text-muted-foreground pt-2 border-t\">\n                  <span className=\"flex items-center gap-1\">\n                    <Download className=\"h-3 w-3\" />\n                    <span className=\"hidden sm:inline\">{(template.downloads || 1200).toLocaleString()} downloads</span>\n                    <span className=\"sm:hidden\">{(template.downloads || 1200) > 1000 ? `${Math.floor((template.downloads || 1200) / 1000)}k` : template.downloads}</span>\n                  </span>\n                  <span className=\"hidden sm:inline\">Updated recently</span>\n                  <span className=\"sm:hidden\">Recent</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {filteredTemplates.length === 0 && !loading && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-400 mb-4\">\n            <Filter className=\"h-12 w-12 sm:h-16 sm:w-16 mx-auto\" />\n          </div>\n          <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No templates found</h3>\n          <p className=\"text-sm sm:text-base text-gray-600 mb-6\">\n            Try adjusting your search criteria or browse all templates\n          </p>\n          <Button onClick={() => {\n            setSearchTerm('');\n            setSelectedCategory('All');\n            setSortBy('title');\n          }}>\n            Clear Filters\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAM;IAE9D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,IAAI,MAAM,SAAS,EAAE;gBACnB,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,YAAY;QAEpC,OAAO;YACL,OAAO,mBAAmB,CAAC,YAAY;QACzC;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAW;QAAY;QAAkB;KAAO;IAEpD,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAC/E,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACpC,MAAM,gBAAgB,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACnD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;oBACb,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;oBACjD,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;gBACnD,CAAC;YAED,aAAa;YAEb,4BAA4B;YAC5B,MAAM,mBAAmB;gBAAC;mBAAU,IAAI,IAAI,eAAe,IAAI,CAAA,IAAK,EAAE,QAAQ,KAAK,EAAE;aAAE;YACvF,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,wBAAwB;YACxB,MAAM,gBAAgB;gBACpB;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAc;wBAAU;qBAAO;oBACtC,WAAW,IAAI;oBACf,WAAW,IAAI;oBACf,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAc;wBAAU;qBAAO;oBACtC,WAAW,IAAI;oBACf,WAAW,IAAI;oBACf,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,UAAU;oBACV,MAAM;wBAAC;wBAAc;wBAAU;qBAAO;oBACtC,WAAW,IAAI;oBACf,WAAW,IAAI;oBACf,WAAW;gBACb;aACD;YAED,aAAa;YACb,cAAc;gBAAC;gBAAO;gBAAa;gBAAc;aAAY;QAC/D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,WACzB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;QAEvE;QAEA,qBAAqB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;QAC/D;QAEA,iBAAiB;QACjB,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,OAAQ;gBACN,KAAK;oBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B,KAAK;oBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B,KAAK;gBACL;oBACE,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;YACxC;QACF;QAEA,qBAAqB;IACvB;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,+DAA+D;QAC/D,IAAI,CAAC,UAAU,eAAe,CAAC,UAAU,UAAU;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,WAAW,GAAG,SAAS,WAAW,IAAI,KAAK,CAAC,EAAE,SAAS,WAAW,EAAE;gBACpE,SAAS,CAAC,cAAc,EAAE,SAAS,KAAK,EAAE;gBAC1C,SAAS,CAAC,0BAA0B,EAAE,SAAS,KAAK,CAAC,6FAA6F,CAAC;gBACnJ,MAAM;gBACN,YAAY,SAAS,EAAE;gBACvB,eAAe,SAAS,KAAK;gBAC7B,QAAQ;YACV;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,+DAA+D;QAC/D,IAAI,CAAC,UAAU,eAAe,CAAC,UAAU,UAAU;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE;gBACzB,QAAQ,KAAK,GAAG;gBAChB,WAAW,KAAK,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,WAAW,GAAG,SAAS,WAAW,IAAI,KAAK,CAAC,EAAE,SAAS,WAAW,EAAE;gBACpE,SAAS,CAAC,qBAAqB,EAAE,SAAS,KAAK,EAAE;gBACjD,SAAS,CAAC,iCAAiC,EAAE,SAAS,KAAK,CAAC,oEAAoE,CAAC;gBACjI,MAAM;gBACN,YAAY,SAAS,EAAE;gBACvB,eAAe,SAAS,KAAK;gBAC7B,QAAQ;YACV;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAEvC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJR;;;;;;;;;;;;;;;;IAWrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;kDACnC,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC;gCAAG,WAAU;0CAAuI;;;;;;0CAGrJ,8OAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;kCAOzF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA8C;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA+C;;;;;;kDAC9D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAE5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgD;;;;;;kDAC/D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;0BAMhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAGjD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAQ,eAAe;;kDACpC,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;0DAC1B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,aAAa,mBAAmB,YAAY;wCACrD,MAAK;wCACL,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;4CAET;4CACA,aAAa,uBACZ,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;;;;;;;uCATrD;;;;;;;;;;;;;;;;kCAkBb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCACK,kBAAkB,MAAM;oCAAC;oCAAK,UAAU,MAAM;oCAAC;;;;;;;4BAEzD,4BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAEhC,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,QAAQ,iBAChB,8OAAC;wCACC,KAAK,SAAS,QAAQ;wCACtB,KAAK,SAAS,KAAK;wCACnB,WAAU;;;;;6DAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAE,WAAU;8DAAyD;;;;;;;;;;;;;;;;;kDAM5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;;kEAEzC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAuC;;;;;;;0DAGxD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAuC;;;;;;;;;;;;;kDAMnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,SAAS,QAAQ;;;;;;;;;;;kDAKtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;sDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,SAAS,KAAK;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAkC,SAAS,MAAM,IAAI;;;;;;;;;;;;;;;;;;sDAGzE,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,SAAS,WAAW;;;;;;;;;;;;;;;;;0CAK3B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;0DAC7C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;0DAC7C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;kDAI/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;;oEAAgD;oEAAE,SAAS,KAAK;;;;;;;;;;;;;kEAElF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEAA6C;oEAAE,KAAK,KAAK,CAAC,SAAS,KAAK,GAAG;;;;;;;0EAC1F,8OAAC;gEAAI,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAKxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;;kFAEzC,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,8OAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;0EAG9B,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS,IAAM,iBAAiB;;kFAEhC,8OAAC,wNAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;kFACzB,8OAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,8OAAC;wEAAK,WAAU;kFAAY;;;;;;;;;;;;;;;;;;kEAIhC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS,IAAM,qBAAqB;;0EAEpC,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;0EACnC,8OAAC;gEAAK,WAAU;0EAAY;;;;;;;;;;;;;;;;;;0DAKhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;;oEAAoB,CAAC,SAAS,SAAS,IAAI,IAAI,EAAE,cAAc;oEAAG;;;;;;;0EAClF,8OAAC;gEAAK,WAAU;0EAAa,CAAC,SAAS,SAAS,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,GAAG,SAAS,SAAS;;;;;;;;;;;;kEAE/I,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;uBA9IzB,SAAS,EAAE;;;;;;;;;;YAsJzB,kBAAkB,MAAM,KAAK,KAAK,CAAC,yBAClC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAGvD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;4BACf,cAAc;4BACd,oBAAoB;4BACpB,UAAU;wBACZ;kCAAG;;;;;;;;;;;;;;;;;;AAOb", "debugId": null}}]}