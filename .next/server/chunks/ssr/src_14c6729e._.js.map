{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/toast.ts"], "sourcesContent": ["// Simple toast utility\nexport const toast = {\n  success: (message: string) => {\n    alert(`✅ ${message}`);\n  },\n  error: (message: string) => {\n    alert(`❌ ${message}`);\n  },\n  info: (message: string) => {\n    alert(`ℹ️ ${message}`);\n  }\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AAChB,MAAM,QAAQ;IACnB,SAAS,CAAC;QACR,MAAM,CAAC,EAAE,EAAE,SAAS;IACtB;IACA,OAAO,CAAC;QACN,MAAM,CAAC,EAAE,EAAE,SAAS;IACtB;IACA,MAAM,CAAC;QACL,MAAM,CAAC,GAAG,EAAE,SAAS;IACvB;AACF", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/EnhancedTemplateDialog.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Switch } from '@/components/ui/switch';\nimport { Badge } from '@/components/ui/badge';\nimport { X, Plus, FileText, DollarSign, Tag, Code, Link, Star } from 'lucide-react';\nimport { Template } from '@/types';\nimport { collection, addDoc, doc, updateDoc } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { toast } from '@/lib/toast';\n\ninterface TemplateDialogProps {\n  open: boolean;\n  onClose: () => void;\n  template?: Template | null;\n  onSuccess: () => void;\n}\n\nexport default function EnhancedTemplateDialog({ open, onClose, template, onSuccess }: TemplateDialogProps) {\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    title: '',\n    shortDescription: '',\n    detailedDescription: '',\n    category: '',\n    price: '',\n    originalPrice: '',\n    imageUrl: '',\n    previewUrl: '',\n    demoUrl: '',\n    downloadUrl: '',\n    featured: false,\n    premium: false,\n    free: false,\n    version: '1.0.0',\n    setupTime: '',\n    difficultyLevel: 'Beginner',\n    licenseType: 'Standard License',\n    keyFeatures: '',\n    technologyStack: ''\n  });\n\n  useEffect(() => {\n    if (template) {\n      setFormData({\n        title: template.title || '',\n        shortDescription: template.description || '',\n        detailedDescription: template.detailedDescription || '',\n        category: template.category || '',\n        price: template.price?.toString() || '',\n        originalPrice: template.originalPrice?.toString() || '',\n        imageUrl: template.imageUrl || '',\n        previewUrl: template.previewUrl || '',\n        demoUrl: template.demoUrl || '',\n        downloadUrl: template.downloadUrl || '',\n        featured: template.featured || false,\n        premium: template.premium || false,\n        free: template.free || false,\n        version: template.version || '1.0.0',\n        setupTime: template.setupTime || '',\n        difficultyLevel: template.difficultyLevel || 'Beginner',\n        licenseType: template.licenseType || 'Standard License',\n        keyFeatures: template.keyFeatures || '',\n        technologyStack: template.technologyStack || ''\n      });\n    } else {\n      setFormData({\n        title: '',\n        shortDescription: '',\n        detailedDescription: '',\n        category: '',\n        price: '',\n        originalPrice: '',\n        imageUrl: '',\n        previewUrl: '',\n        demoUrl: '',\n        downloadUrl: '',\n        featured: false,\n        premium: false,\n        free: false,\n        version: '1.0.0',\n        setupTime: '',\n        difficultyLevel: 'Beginner',\n        licenseType: 'Standard License',\n        keyFeatures: '',\n        technologyStack: ''\n      });\n    }\n  }, [template, open]);\n\n  const handleInputChange = (field: string, value: string | boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Auto-calculate discount\n  const calculateDiscount = () => {\n    if (formData.price && formData.originalPrice) {\n      const price = parseFloat(formData.price);\n      const originalPrice = parseFloat(formData.originalPrice);\n      if (originalPrice > price) {\n        return Math.round(((originalPrice - price) / originalPrice) * 100);\n      }\n    }\n    return 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const templateData = {\n        title: formData.title,\n        description: formData.shortDescription,\n        detailedDescription: formData.detailedDescription,\n        category: formData.category,\n        price: parseFloat(formData.price) || 0,\n        originalPrice: formData.originalPrice ? parseFloat(formData.originalPrice) : undefined,\n        imageUrl: formData.imageUrl,\n        previewUrl: formData.previewUrl || undefined,\n        demoUrl: formData.demoUrl || undefined,\n        downloadUrl: formData.downloadUrl || undefined,\n        featured: formData.featured,\n        premium: formData.premium,\n        free: formData.free,\n        version: formData.version,\n        setupTime: formData.setupTime,\n        difficultyLevel: formData.difficultyLevel,\n        licenseType: formData.licenseType,\n        keyFeatures: formData.keyFeatures,\n        technologyStack: formData.technologyStack,\n        discount: calculateDiscount(),\n        rating: template?.rating || 0,\n        downloads: template?.downloads || 0,\n        updatedAt: new Date(),\n        createdBy: 'admin'\n      };\n\n      if (template) {\n        await updateDoc(doc(db, 'templates', template.id), templateData);\n        toast.success('Template updated successfully');\n      } else {\n        await addDoc(collection(db, 'templates'), {\n          ...templateData,\n          createdAt: new Date()\n        });\n        toast.success('Template added successfully');\n      }\n\n      onSuccess();\n      onClose();\n    } catch (error) {\n      console.error('Error saving template:', error);\n      toast.error('Failed to save template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Plus className=\"h-5 w-5\" />\n            {template ? 'Edit Template' : 'Add New Template'}\n          </DialogTitle>\n          <DialogDescription>\n            Create a new template with all the necessary details and pricing information\n          </DialogDescription>\n        </DialogHeader>\n\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Basic Information Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-blue-600\">\n              <FileText className=\"h-5 w-5\" />\n              Basic Information\n            </div>\n            <p className=\"text-sm text-gray-600\">Essential template details and descriptions</p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"title\">Template Title *</Label>\n                <Input\n                  id=\"title\"\n                  value={formData.title}\n                  onChange={(e) => handleInputChange('title', e.target.value)}\n                  placeholder=\"Enter template title...\"\n                  required\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"urlSlug\">URL Slug</Label>\n                <Input\n                  id=\"urlSlug\"\n                  value={formData.title.toLowerCase().replace(/\\s+/g, '-')}\n                  placeholder=\"auto-generated-from-title\"\n                  disabled\n                  className=\"bg-gray-50\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Auto-generated from title if left empty</p>\n              </div>\n            </div>\n\n            <div>\n              <Label htmlFor=\"shortDescription\">Short Description *</Label>\n              <Textarea\n                id=\"shortDescription\"\n                value={formData.shortDescription}\n                onChange={(e) => handleInputChange('shortDescription', e.target.value)}\n                placeholder=\"Brief description for template cards and listings...\"\n                rows={2}\n                maxLength={150}\n                required\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">This appears on template cards (max 150 characters recommended)</p>\n            </div>\n\n            <div>\n              <Label htmlFor=\"detailedDescription\">Detailed Description</Label>\n              <Textarea\n                id=\"detailedDescription\"\n                value={formData.detailedDescription}\n                onChange={(e) => handleInputChange('detailedDescription', e.target.value)}\n                placeholder=\"Comprehensive description including features, use cases, and benefits...\"\n                rows={4}\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">Detailed description for template detail pages</p>\n            </div>\n          </div>\n\n          {/* Pricing & Revenue Section */}\n          <div className=\"space-y-4 bg-green-50 p-4 rounded-lg\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-green-600\">\n              <DollarSign className=\"h-5 w-5\" />\n              Pricing & Revenue\n            </div>\n            <p className=\"text-sm text-gray-600\">Set competitive pricing with automatic discount calculation</p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"price\">Current Price (₹) *</Label>\n                <Input\n                  id=\"price\"\n                  type=\"number\"\n                  value={formData.price}\n                  onChange={(e) => handleInputChange('price', e.target.value)}\n                  placeholder=\"999\"\n                  min=\"0\"\n                  required\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Final price customers will pay</p>\n              </div>\n              <div>\n                <Label htmlFor=\"originalPrice\">Original Price (₹) <span className=\"text-gray-500\">Optional</span></Label>\n                <Input\n                  id=\"originalPrice\"\n                  type=\"number\"\n                  value={formData.originalPrice}\n                  onChange={(e) => handleInputChange('originalPrice', e.target.value)}\n                  placeholder=\"1499\"\n                  min=\"0\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Higher price to show discount value</p>\n              </div>\n              <div>\n                <Label htmlFor=\"discount\">Discount % <span className=\"text-green-600\">Auto-calculated</span></Label>\n                <Input\n                  id=\"discount\"\n                  type=\"number\"\n                  value={calculateDiscount()}\n                  placeholder=\"33\"\n                  disabled\n                  className=\"bg-gray-50\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Automatically calculated from prices</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Category & Classification Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-purple-600\">\n              <Tag className=\"h-5 w-5\" />\n              Category & Classification\n            </div>\n            <p className=\"text-sm text-gray-600\">Organize and classify your template for better discovery</p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"category\">Template Category *</Label>\n                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select a category\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Technology\">Technology</SelectItem>\n                    <SelectItem value=\"Business\">Business</SelectItem>\n                    <SelectItem value=\"Education\">Education</SelectItem>\n                    <SelectItem value=\"Portfolio\">Portfolio</SelectItem>\n                    <SelectItem value=\"E-commerce\">E-commerce</SelectItem>\n                    <SelectItem value=\"Dashboard\">Dashboard</SelectItem>\n                    <SelectItem value=\"Blog\">Blog</SelectItem>\n                  </SelectContent>\n                </Select>\n                <p className=\"text-xs text-gray-500 mt-1\">Choose the most relevant category</p>\n              </div>\n              <div>\n                <Label htmlFor=\"difficultyLevel\">Difficulty Level</Label>\n                <Select value={formData.difficultyLevel} onValueChange={(value) => handleInputChange('difficultyLevel', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Beginner\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Beginner\">🟢 Beginner</SelectItem>\n                    <SelectItem value=\"Intermediate\">🟡 Intermediate</SelectItem>\n                    <SelectItem value=\"Advanced\">🔴 Advanced</SelectItem>\n                  </SelectContent>\n                </Select>\n                <p className=\"text-xs text-gray-500 mt-1\">Implementation complexity level</p>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"version\">Version</Label>\n                <Input\n                  id=\"version\"\n                  value={formData.version}\n                  onChange={(e) => handleInputChange('version', e.target.value)}\n                  placeholder=\"1.0.0\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Semantic version number</p>\n              </div>\n              <div>\n                <Label htmlFor=\"setupTime\">Setup Time</Label>\n                <Input\n                  id=\"setupTime\"\n                  value={formData.setupTime}\n                  onChange={(e) => handleInputChange('setupTime', e.target.value)}\n                  placeholder=\"2-3 hours\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Time to implement/customize</p>\n              </div>\n              <div>\n                <Label htmlFor=\"licenseType\">License Type</Label>\n                <Select value={formData.licenseType} onValueChange={(value) => handleInputChange('licenseType', value)}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Standard License\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"Standard License\">Standard License</SelectItem>\n                    <SelectItem value=\"Extended License\">Extended License</SelectItem>\n                    <SelectItem value=\"Commercial License\">Commercial License</SelectItem>\n                  </SelectContent>\n                </Select>\n                <p className=\"text-xs text-gray-500 mt-1\">Usage rights and restrictions</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Features & Technology Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-blue-600\">\n              <Code className=\"h-5 w-5\" />\n              Features & Technology\n            </div>\n            <p className=\"text-sm text-gray-600\">Highlight key features and technical specifications</p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"keyFeatures\">Key Features</Label>\n                <Textarea\n                  id=\"keyFeatures\"\n                  value={formData.keyFeatures}\n                  onChange={(e) => handleInputChange('keyFeatures', e.target.value)}\n                  placeholder=\"Responsive Design, Dark Mode, SEO Optimized, Mobile First, Fast Loading\"\n                  rows={3}\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Separate features with commas. These will be displayed as badges.</p>\n              </div>\n              <div>\n                <Label htmlFor=\"technologyStack\">Technology Stack</Label>\n                <Textarea\n                  id=\"technologyStack\"\n                  value={formData.technologyStack}\n                  onChange={(e) => handleInputChange('technologyStack', e.target.value)}\n                  placeholder=\"Next.js 15, TypeScript, Tailwind CSS, Framer Motion, Supabase\"\n                  rows={3}\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">List technologies used. Helps developers understand requirements.</p>\n              </div>\n            </div>\n          </div>\n\n          {/* URLs & Media Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-orange-600\">\n              <Link className=\"h-5 w-5\" />\n              URLs & Media\n            </div>\n            <p className=\"text-sm text-gray-600\">Add preview images and demo links</p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"imageUrl\">Preview Image URL *</Label>\n                <Input\n                  id=\"imageUrl\"\n                  type=\"url\"\n                  value={formData.imageUrl}\n                  onChange={(e) => handleInputChange('imageUrl', e.target.value)}\n                  placeholder=\"https://example.com/template-preview.jpg\"\n                  required\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">High-quality image for template cards (recommended: 800x600px)</p>\n              </div>\n              <div>\n                <Label htmlFor=\"previewUrl\">Live Preview URL</Label>\n                <Input\n                  id=\"previewUrl\"\n                  type=\"url\"\n                  value={formData.previewUrl}\n                  onChange={(e) => handleInputChange('previewUrl', e.target.value)}\n                  placeholder=\"https://preview.example.com\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Working demo for customers to preview</p>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"demoUrl\">Demo URL</Label>\n                <Input\n                  id=\"demoUrl\"\n                  type=\"url\"\n                  value={formData.demoUrl}\n                  onChange={(e) => handleInputChange('demoUrl', e.target.value)}\n                  placeholder=\"https://demo.example.com\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Interactive demo or showcase</p>\n              </div>\n              <div>\n                <Label htmlFor=\"downloadUrl\">Download URL</Label>\n                <Input\n                  id=\"downloadUrl\"\n                  type=\"url\"\n                  value={formData.downloadUrl}\n                  onChange={(e) => handleInputChange('downloadUrl', e.target.value)}\n                  placeholder=\"https://files.example.com/template.zip\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">Direct download link for purchased templates</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Template Status & Visibility Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-2 text-lg font-semibold text-purple-600\">\n              <Star className=\"h-5 w-5\" />\n              Template Status & Visibility\n            </div>\n            <p className=\"text-sm text-gray-600\">Control how your template appears to customers</p>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"featured\"\n                  checked={formData.featured}\n                  onCheckedChange={(checked) => handleInputChange('featured', checked)}\n                />\n                <div>\n                  <Label htmlFor=\"featured\" className=\"font-medium\">Featured Template</Label>\n                  <p className=\"text-xs text-gray-500\">Appears in featured sections and gets priority placement</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"premium\"\n                  checked={formData.premium}\n                  onCheckedChange={(checked) => handleInputChange('premium', checked)}\n                />\n                <div>\n                  <Label htmlFor=\"premium\" className=\"font-medium\">Premium Template</Label>\n                  <p className=\"text-xs text-gray-500\">High-quality template with advanced features</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <Switch\n                  id=\"free\"\n                  checked={formData.free}\n                  onCheckedChange={(checked) => handleInputChange('free', checked)}\n                />\n                <div>\n                  <Label htmlFor=\"free\" className=\"font-medium\">Free Template</Label>\n                  <p className=\"text-xs text-gray-500\">Available for free download without payment</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <Label className=\"font-medium\">Template Status Summary:</Label>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {formData.featured && formData.premium && formData.free ? 'Featured Free Premium Template' :\n                 formData.featured && formData.premium ? 'Featured Premium Template' :\n                 formData.featured && formData.free ? 'Featured Free Template' :\n                 formData.premium && formData.free ? 'Free Premium Template' :\n                 formData.featured ? 'Featured Template' :\n                 formData.premium ? 'Premium Template' :\n                 formData.free ? 'Free Template' :\n                 'Standard Template'}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between pt-4\">\n            <Button type=\"button\" variant=\"outline\" onClick={onClose}>\n              <X className=\"h-4 w-4 mr-2\" />\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={loading} className=\"bg-blue-600 hover:bg-blue-700\">\n              {loading ? 'Creating...' : (\n                <>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Create Template\n                </>\n              )}\n            </Button>\n          </div>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AAfA;;;;;;;;;;;;;;AAwBe,SAAS,uBAAuB,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAuB;IACxG,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,OAAO;QACP,eAAe;QACf,UAAU;QACV,YAAY;QACZ,SAAS;QACT,aAAa;QACb,UAAU;QACV,SAAS;QACT,MAAM;QACN,SAAS;QACT,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,aAAa;QACb,iBAAiB;IACnB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,YAAY;gBACV,OAAO,SAAS,KAAK,IAAI;gBACzB,kBAAkB,SAAS,WAAW,IAAI;gBAC1C,qBAAqB,SAAS,mBAAmB,IAAI;gBACrD,UAAU,SAAS,QAAQ,IAAI;gBAC/B,OAAO,SAAS,KAAK,EAAE,cAAc;gBACrC,eAAe,SAAS,aAAa,EAAE,cAAc;gBACrD,UAAU,SAAS,QAAQ,IAAI;gBAC/B,YAAY,SAAS,UAAU,IAAI;gBACnC,SAAS,SAAS,OAAO,IAAI;gBAC7B,aAAa,SAAS,WAAW,IAAI;gBACrC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,SAAS,SAAS,OAAO,IAAI;gBAC7B,MAAM,SAAS,IAAI,IAAI;gBACvB,SAAS,SAAS,OAAO,IAAI;gBAC7B,WAAW,SAAS,SAAS,IAAI;gBACjC,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa,SAAS,WAAW,IAAI;gBACrC,aAAa,SAAS,WAAW,IAAI;gBACrC,iBAAiB,SAAS,eAAe,IAAI;YAC/C;QACF,OAAO;YACL,YAAY;gBACV,OAAO;gBACP,kBAAkB;gBAClB,qBAAqB;gBACrB,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,UAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,WAAW;gBACX,iBAAiB;gBACjB,aAAa;gBACb,aAAa;gBACb,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAU;KAAK;IAEnB,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,0BAA0B;IAC1B,MAAM,oBAAoB;QACxB,IAAI,SAAS,KAAK,IAAI,SAAS,aAAa,EAAE;YAC5C,MAAM,QAAQ,WAAW,SAAS,KAAK;YACvC,MAAM,gBAAgB,WAAW,SAAS,aAAa;YACvD,IAAI,gBAAgB,OAAO;gBACzB,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,KAAK,IAAI,gBAAiB;YAChE;QACF;QACA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,eAAe;gBACnB,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,gBAAgB;gBACtC,qBAAqB,SAAS,mBAAmB;gBACjD,UAAU,SAAS,QAAQ;gBAC3B,OAAO,WAAW,SAAS,KAAK,KAAK;gBACrC,eAAe,SAAS,aAAa,GAAG,WAAW,SAAS,aAAa,IAAI;gBAC7E,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU,IAAI;gBACnC,SAAS,SAAS,OAAO,IAAI;gBAC7B,aAAa,SAAS,WAAW,IAAI;gBACrC,UAAU,SAAS,QAAQ;gBAC3B,SAAS,SAAS,OAAO;gBACzB,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,OAAO;gBACzB,WAAW,SAAS,SAAS;gBAC7B,iBAAiB,SAAS,eAAe;gBACzC,aAAa,SAAS,WAAW;gBACjC,aAAa,SAAS,WAAW;gBACjC,iBAAiB,SAAS,eAAe;gBACzC,UAAU;gBACV,QAAQ,UAAU,UAAU;gBAC5B,WAAW,UAAU,aAAa;gBAClC,WAAW,IAAI;gBACf,WAAW;YACb;YAEA,IAAI,UAAU;gBACZ,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,aAAa,SAAS,EAAE,GAAG;gBACnD,mHAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc;oBACxC,GAAG,YAAY;oBACf,WAAW,IAAI;gBACjB;gBACA,mHAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mHAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACf,WAAW,kBAAkB;;;;;;;sCAEhC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;oDACpD,aAAY;oDACZ,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;4CACZ,MAAM;4CACN,WAAW;4CACX,QAAQ;;;;;;sDAEV,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAG5C,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAsB;;;;;;sDACrC,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,SAAS,mBAAmB;4CACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACxE,aAAY;4CACZ,MAAM;;;;;;sDAER,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAQ;;;;;;8DACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,aAAY;oDACZ,KAAI;oDACJ,QAAQ;;;;;;8DAEV,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAgB;sEAAmB,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAClF,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,aAAY;oDACZ,KAAI;;;;;;8DAEN,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;;wDAAW;sEAAW,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DACtE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,aAAY;oDACZ,QAAQ;oDACR,WAAU;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,QAAQ;oDAAE,eAAe,CAAC,QAAU,kBAAkB,YAAY;;sEACxF,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAa;;;;;;8EAC/B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAY;;;;;;8EAC9B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;;;;;;;;;;;;;8DAG7B,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,eAAe;oDAAE,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;;sEACtG,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;8EAC7B,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAe;;;;;;8EACjC,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAW;;;;;;;;;;;;;;;;;;8DAGjC,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAY;;;;;;8DAC3B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,WAAW;oDAAE,eAAe,CAAC,QAAU,kBAAkB,eAAe;;sEAC9F,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAmB;;;;;;8EACrC,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAmB;;;;;;8EACrC,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAqB;;;;;;;;;;;;;;;;;;8DAG3C,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;;;;;;8DAER,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,SAAS,eAAe;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDACpE,aAAY;oDACZ,MAAM;;;;;;8DAER,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;oDACZ,QAAQ;;;;;;8DAEV,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa;;;;;;8DAC5B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC/D,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAI9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAE5C,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG9B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAErC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS,SAAS,QAAQ;oDAC1B,iBAAiB,CAAC,UAAY,kBAAkB,YAAY;;;;;;8DAE9D,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAW,WAAU;sEAAc;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS,SAAS,OAAO;oDACzB,iBAAiB,CAAC,UAAY,kBAAkB,WAAW;;;;;;8DAE7D,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAc;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS,SAAS,IAAI;oDACtB,iBAAiB,CAAC,UAAY,kBAAkB,QAAQ;;;;;;8DAE1D,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAc;;;;;;sEAC9C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAc;;;;;;sDAC/B,8OAAC;4CAAE,WAAU;sDACV,SAAS,QAAQ,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,GAAG,mCACzD,SAAS,QAAQ,IAAI,SAAS,OAAO,GAAG,8BACxC,SAAS,QAAQ,IAAI,SAAS,IAAI,GAAG,2BACrC,SAAS,OAAO,IAAI,SAAS,IAAI,GAAG,0BACpC,SAAS,QAAQ,GAAG,sBACpB,SAAS,OAAO,GAAG,qBACnB,SAAS,IAAI,GAAG,kBAChB;;;;;;;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;oCAAU,SAAS;;sDAC/C,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;oCAAS,WAAU;8CAChD,UAAU,8BACT;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD", "debugId": null}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/components/admin/TemplatesTab.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Plus, Download, Edit, Trash2, ExternalLink, Search, Filter } from 'lucide-react';\nimport { Template } from '@/types';\nimport { collection, getDocs, query, orderBy, deleteDoc, doc } from 'firebase/firestore';\nimport { db } from '@/lib/firebase';\nimport { toast } from '@/lib/toast';\nimport EnhancedTemplateDialog from './EnhancedTemplateDialog';\n\ninterface TemplatesTabProps {\n  onRefresh?: () => void;\n}\n\nexport default function TemplatesTab({ onRefresh }: TemplatesTabProps) {\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [sortBy, setSortBy] = useState<'title' | 'price' | 'category' | 'createdAt'>('createdAt');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');\n  const [showAddDialog, setShowAddDialog] = useState(false);\n  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n\n  useEffect(() => {\n    fetchTemplates();\n  }, []);\n\n  const fetchTemplates = async () => {\n    try {\n      setLoading(true);\n      const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));\n      const querySnapshot = await getDocs(templatesQuery);\n      const templatesData = querySnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data(),\n        createdAt: doc.data().createdAt?.toDate() || new Date(),\n        updatedAt: doc.data().updatedAt?.toDate() || new Date(),\n      })) as Template[];\n\n      setTemplates(templatesData);\n    } catch (error) {\n      console.error('Error fetching templates:', error);\n      // Fallback to sample data\n      setTemplates([\n        {\n          id: '1',\n          title: 'SaaS Dashboard Pro',\n          description: 'Professional dashboard template for SaaS applications',\n          category: 'Technology',\n          price: 2499,\n          imageUrl: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',\n          previewUrl: '#',\n          tags: ['React', 'Dashboard', 'SaaS'],\n          featured: true,\n          rating: 4.9,\n          downloads: 1234,\n          createdAt: new Date('2023-06-01'),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        },\n        {\n          id: '2',\n          title: 'Free Startup Landing',\n          description: 'Clean and minimal landing page for startups',\n          category: 'Business',\n          price: 0,\n          imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',\n          previewUrl: '#',\n          tags: ['Landing Page', 'Startup'],\n          featured: false,\n          rating: 4.7,\n          downloads: 856,\n          createdAt: new Date('2023-06-02'),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        },\n        {\n          id: '3',\n          title: 'Education Platform',\n          description: 'Complete education platform template with course management',\n          category: 'Education',\n          price: 3499,\n          imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',\n          previewUrl: '#',\n          tags: ['Education', 'LMS'],\n          featured: true,\n          rating: 4.8,\n          downloads: 567,\n          createdAt: new Date('2023-06-03'),\n          updatedAt: new Date(),\n          createdBy: 'admin'\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredAndSortedTemplates = templates\n    .filter(template => {\n      const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           template.description.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesCategory = filterCategory === 'all' || template.category === filterCategory;\n      return matchesSearch && matchesCategory;\n    })\n    .sort((a, b) => {\n      const aVal = a[sortBy];\n      const bVal = b[sortBy];\n\n      if (sortBy === 'price') {\n        return sortOrder === 'asc' ? aVal - bVal : bVal - aVal;\n      }\n\n      if (sortBy === 'createdAt') {\n        return sortOrder === 'asc' \n          ? new Date(aVal).getTime() - new Date(bVal).getTime()\n          : new Date(bVal).getTime() - new Date(aVal).getTime();\n      }\n\n      const comparison = String(aVal).localeCompare(String(bVal));\n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this template?')) return;\n\n    try {\n      await deleteDoc(doc(db, 'templates', id));\n      toast.success('Template deleted successfully');\n      await fetchTemplates();\n      onRefresh?.();\n    } catch (error) {\n      console.error('Error deleting template:', error);\n      toast.error('Failed to delete template');\n    }\n  };\n\n  const exportToCSV = () => {\n    const headers = ['Title', 'Description', 'Price', 'Category', 'Preview URL', 'Created At'];\n    const csvData = [\n      headers,\n      ...filteredAndSortedTemplates.map(template => [\n        template.title,\n        template.description,\n        template.price.toString(),\n        template.category,\n        template.previewUrl || '',\n        new Date(template.createdAt).toLocaleDateString()\n      ])\n    ];\n\n    const csvContent = csvData.map(row =>\n      row.map(field => `\"${field}\"`).join(',')\n    ).join('\\n');\n\n    const blob = new Blob([csvContent], { type: 'text/csv' });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `templates-${new Date().toISOString().split('T')[0]}.csv`;\n    a.click();\n    window.URL.revokeObjectURL(url);\n  };\n\n  const categories = ['all', ...new Set(templates.map(t => t.category))];\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <CardTitle>Templates Management</CardTitle>\n            <CardDescription>\n              Manage your template collection\n            </CardDescription>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button onClick={() => setShowAddDialog(true)}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Template\n            </Button>\n            <Button variant=\"outline\" onClick={exportToCSV}>\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export CSV\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* Filters and Search */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                placeholder=\"Search templates...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n          <Select value={filterCategory} onValueChange={setFilterCategory}>\n            <SelectTrigger className=\"w-[180px]\">\n              <Filter className=\"h-4 w-4 mr-2\" />\n              <SelectValue placeholder=\"Category\" />\n            </SelectTrigger>\n            <SelectContent>\n              {categories.map(category => (\n                <SelectItem key={category} value={category}>\n                  {category === 'all' ? 'All Categories' : category}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n            const [field, order] = value.split('-');\n            setSortBy(field as any);\n            setSortOrder(order as 'asc' | 'desc');\n          }}>\n            <SelectTrigger className=\"w-[180px]\">\n              <SelectValue placeholder=\"Sort by\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"createdAt-desc\">Newest First</SelectItem>\n              <SelectItem value=\"createdAt-asc\">Oldest First</SelectItem>\n              <SelectItem value=\"title-asc\">Title A-Z</SelectItem>\n              <SelectItem value=\"title-desc\">Title Z-A</SelectItem>\n              <SelectItem value=\"price-asc\">Price Low-High</SelectItem>\n              <SelectItem value=\"price-desc\">Price High-Low</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Templates List */}\n        <div className=\"space-y-4\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Loading templates...</p>\n            </div>\n          ) : filteredAndSortedTemplates.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No templates found\n            </div>\n          ) : (\n            filteredAndSortedTemplates.map((template) => (\n              <div key={template.id} className=\"border rounded-lg p-4 hover:bg-gray-50\">\n                <div className=\"flex justify-between items-start mb-2\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <h4 className=\"font-semibold text-lg\">{template.title}</h4>\n                      <Badge variant=\"secondary\">{template.category}</Badge>\n                      {template.featured && <Badge variant=\"default\">Featured</Badge>}\n                    </div>\n                    <p className=\"text-sm text-muted-foreground mb-2\">\n                      {template.description}\n                    </p>\n                    <div className=\"flex items-center gap-4 text-sm\">\n                      <span className=\"font-medium text-green-600\">\n                        {template.price === 0 ? 'Free' : `₹${template.price}`}\n                      </span>\n                      {template.previewUrl && (\n                        <a\n                          href={template.previewUrl}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-blue-600 hover:text-blue-800 flex items-center gap-1\"\n                        >\n                          <ExternalLink className=\"h-3 w-3\" />\n                          Preview Link\n                        </a>\n                      )}\n                      <span className=\"text-muted-foreground\">\n                        Created: {new Date(template.createdAt).toLocaleDateString()}\n                      </span>\n                    </div>\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => setEditingTemplate(template)}\n                    >\n                      <Edit className=\"h-4 w-4\" />\n                    </Button>\n                    <Button\n                      variant=\"destructive\"\n                      size=\"sm\"\n                      onClick={() => handleDelete(template.id)}\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </CardContent>\n\n      {/* Add/Edit Template Dialog */}\n      <EnhancedTemplateDialog\n        open={showAddDialog || !!editingTemplate}\n        onClose={() => {\n          setShowAddDialog(false);\n          setEditingTemplate(null);\n        }}\n        template={editingTemplate}\n        onSuccess={() => {\n          setShowAddDialog(false);\n          setEditingTemplate(null);\n          fetchTemplates();\n          onRefresh?.();\n        }}\n      />\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAmBe,SAAS,aAAa,EAAE,SAAS,EAAqB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgD;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,cAAc,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YAC/E,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACpC,MAAM,gBAAgB,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACnD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;oBACb,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;oBACjD,WAAW,IAAI,IAAI,GAAG,SAAS,EAAE,YAAY,IAAI;gBACnD,CAAC;YAED,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,0BAA0B;YAC1B,aAAa;gBACX;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,MAAM;wBAAC;wBAAS;wBAAa;qBAAO;oBACpC,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,MAAM;wBAAC;wBAAgB;qBAAU;oBACjC,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,MAAM;wBAAC;wBAAa;qBAAM;oBAC1B,UAAU;oBACV,QAAQ;oBACR,WAAW;oBACX,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,WAAW;gBACb;aACD;QACH,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B,UAChC,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACvF,MAAM,kBAAkB,mBAAmB,SAAS,SAAS,QAAQ,KAAK;QAC1E,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,MAAM,OAAO,CAAC,CAAC,OAAO;QACtB,MAAM,OAAO,CAAC,CAAC,OAAO;QAEtB,IAAI,WAAW,SAAS;YACtB,OAAO,cAAc,QAAQ,OAAO,OAAO,OAAO;QACpD;QAEA,IAAI,WAAW,aAAa;YAC1B,OAAO,cAAc,QACjB,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,MAAM,OAAO,KACjD,IAAI,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,MAAM,OAAO;QACvD;QAEA,MAAM,aAAa,OAAO,MAAM,aAAa,CAAC,OAAO;QACrD,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,mDAAmD;QAEhE,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,aAAa;YACrC,mHAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,mHAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU;YAAC;YAAS;YAAe;YAAS;YAAY;YAAe;SAAa;QAC1F,MAAM,UAAU;YACd;eACG,2BAA2B,GAAG,CAAC,CAAA,WAAY;oBAC5C,SAAS,KAAK;oBACd,SAAS,WAAW;oBACpB,SAAS,KAAK,CAAC,QAAQ;oBACvB,SAAS,QAAQ;oBACjB,SAAS,UAAU,IAAI;oBACvB,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;iBAChD;SACF;QAED,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAA,MAC7B,IAAI,GAAG,CAAC,CAAA,QAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MACpC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACtE,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,aAAa;QAAC;WAAU,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;KAAG;IAEtE,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,iBAAiB;;sDACtC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;;sDACjC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC,gIAAA,CAAA,cAAW;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAgB,eAAe;;kDAC5C,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;kDACX,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC,kIAAA,CAAA,aAAU;gDAAgB,OAAO;0DAC/B,aAAa,QAAQ,mBAAmB;+CAD1B;;;;;;;;;;;;;;;;0CAMvB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;gCAAE,eAAe,CAAC;oCACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;oCACnC,UAAU;oCACV,aAAa;gCACf;;kDACE,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAiB;;;;;;0DACnC,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAgB;;;;;;0DAClC,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAa;;;;;;0DAC/B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAa;;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;mCAE7B,2BAA2B,MAAM,KAAK,kBACxC,8OAAC;4BAAI,WAAU;sCAAyC;;;;;mCAIxD,2BAA2B,GAAG,CAAC,CAAC,yBAC9B,8OAAC;gCAAsB,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAyB,SAAS,KAAK;;;;;;sEACrD,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,SAAS,QAAQ;;;;;;wDAC5C,SAAS,QAAQ,kBAAI,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;;;;;;;8DAEjD,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAEvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,SAAS,KAAK,KAAK,IAAI,SAAS,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE;;;;;;wDAEtD,SAAS,UAAU,kBAClB,8OAAC;4DACC,MAAM,SAAS,UAAU;4DACzB,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAEV,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAIxC,8OAAC;4DAAK,WAAU;;gEAAwB;gEAC5B,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;sDAI/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,mBAAmB;8DAElC,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,aAAa,SAAS,EAAE;8DAEvC,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA5ChB,SAAS,EAAE;;;;;;;;;;;;;;;;0BAuD7B,8OAAC,qJAAA,CAAA,UAAsB;gBACrB,MAAM,iBAAiB,CAAC,CAAC;gBACzB,SAAS;oBACP,iBAAiB;oBACjB,mBAAmB;gBACrB;gBACA,UAAU;gBACV,WAAW;oBACT,iBAAiB;oBACjB,mBAAmB;oBACnB;oBACA;gBACF;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/lib/firebaseServices.ts"], "sourcesContent": ["import {\n  collection,\n  doc,\n  getDocs,\n  getDoc,\n  addDoc,\n  updateDoc,\n  query,\n  orderBy,\n  onSnapshot\n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\n// Custom Requests Services\nexport const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'customRequests'), {\n      ...requestData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating custom request:', error);\n    throw error;\n  }\n};\n\nexport const getCustomRequests = async () => {\n  try {\n    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n  } catch (error) {\n    console.error('Error fetching custom requests:', error);\n    throw error;\n  }\n};\n\nexport const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n    \n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n    \n    await updateDoc(doc(db, 'customRequests', requestId), updateData);\n  } catch (error) {\n    console.error('Error updating custom request:', error);\n    throw error;\n  }\n};\n\n// User Management Services\nexport const getAllUsers = async () => {\n  try {\n    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n  } catch (error) {\n    console.error('Error fetching users:', error);\n    throw error;\n  }\n};\n\nexport const getUserById = async (userId: string) => {\n  try {\n    const docRef = doc(db, 'users', userId);\n    const docSnap = await getDoc(docRef);\n    \n    if (docSnap.exists()) {\n      return { id: docSnap.id, ...docSnap.data() } as User;\n    } else {\n      throw new Error('User not found');\n    }\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    throw error;\n  }\n};\n\n// Dashboard Statistics Services\nexport const getDashboardStats = async () => {\n  try {\n    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([\n      getDocs(collection(db, 'users')),\n      getDocs(collection(db, 'templates')),\n      getDocs(collection(db, 'customRequests'))\n    ]);\n\n    const totalUsers = usersSnapshot.size;\n    const totalTemplates = templatesSnapshot.size;\n    const totalRequests = requestsSnapshot.size;\n    \n    // Calculate pending requests\n    const pendingRequests = requestsSnapshot.docs.filter(\n      doc => doc.data().status === 'pending'\n    ).length;\n\n    return {\n      totalUsers,\n      totalTemplates,\n      totalRequests,\n      pendingRequests,\n      totalSales: 0, // Placeholder for sales data\n      customizations: 0 // Placeholder for customizations data\n    };\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    throw error;\n  }\n};\n\n// Real-time listeners\nexport const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {\n  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));\n  \n  return onSnapshot(q, (querySnapshot) => {\n    const requests = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CustomRequest[];\n    callback(requests);\n  });\n};\n\nexport const subscribeToUsers = (callback: (users: User[]) => void) => {\n  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const users = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as User[];\n    callback(users);\n  });\n};\n\n// Contact Messages Services\nexport const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {\n  try {\n    const docRef = await addDoc(collection(db, 'contactMessages'), {\n      ...messageData,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    return docRef.id;\n  } catch (error) {\n    console.error('Error creating contact message:', error);\n    throw error;\n  }\n};\n\nexport const getContactMessages = async () => {\n  try {\n    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n  } catch (error) {\n    console.error('Error fetching contact messages:', error);\n    throw error;\n  }\n};\n\nexport const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {\n  try {\n    const updateData: any = {\n      status,\n      updatedAt: new Date()\n    };\n\n    if (adminNotes) {\n      updateData.adminNotes = adminNotes;\n    }\n\n    await updateDoc(doc(db, 'contactMessages', messageId), updateData);\n  } catch (error) {\n    console.error('Error updating contact message:', error);\n    throw error;\n  }\n};\n\nexport const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {\n  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));\n\n  return onSnapshot(q, (querySnapshot) => {\n    const messages = querySnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as ContactMessage[];\n    callback(messages);\n  });\n};\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA;;;AAIO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB;YAC5D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACvE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,4BAA4B,OAAO,WAAmB,QAAiC;IAClG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,kBAAkB,YAAY;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QAC9D,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE;QAE7B,IAAI,QAAQ,MAAM,IAAI;YACpB,OAAO;gBAAE,IAAI,QAAQ,EAAE;gBAAE,GAAG,QAAQ,IAAI,EAAE;YAAC;QAC7C,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,CAAC,eAAe,mBAAmB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7E,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YACvB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;SACxB;QAED,MAAM,aAAa,cAAc,IAAI;QACrC,MAAM,iBAAiB,kBAAkB,IAAI;QAC7C,MAAM,gBAAgB,iBAAiB,IAAI;QAE3C,6BAA6B;QAC7B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,MAAM,CAClD,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,KAAK,WAC7B,MAAM;QAER,OAAO;YACL;YACA;YACA;YACA;YACA,YAAY;YACZ,gBAAgB,EAAE,sCAAsC;QAC1D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAEvE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAE9D,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,QAAQ,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB;YAC7D,GAAG,WAAW;YACd,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,OAAO,OAAO,EAAE;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;QACxE,MAAM,gBAAgB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;QACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO,WAAmB,QAAkC;IACpG,IAAI;QACF,MAAM,aAAkB;YACtB;YACA,WAAW,IAAI;QACjB;QAEA,IAAI,YAAY;YACd,WAAW,UAAU,GAAG;QAC1B;QAEA,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,mBAAmB,YAAY;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,CAAC;IACzC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,oBAAoB,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAExE,OAAO,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,GAAG,CAAC;QACpB,MAAM,WAAW,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC9C,IAAI,IAAI,EAAE;gBACV,GAAG,IAAI,IAAI,EAAE;YACf,CAAC;QACD,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 3062, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Users,\n  FileText,\n  ShoppingCart,\n  DollarSign,\n  Plus,\n  Eye,\n  Settings,\n  BarChart3,\n  Database,\n  MessageSquare,\n  Palette,\n  Download,\n  Phone\n} from 'lucide-react';\nimport TemplatesTab from '@/components/admin/TemplatesTab';\nimport Link from 'next/link';\nimport {\n  getDashboardStats,\n  getCustomRequests,\n  getAllUsers,\n  updateCustomRequestStatus,\n  subscribeToCustomRequests,\n  getContactMessages,\n  updateContactMessageStatus,\n  subscribeToContactMessages\n} from '@/lib/firebaseServices';\nimport { CustomRequest, User, ContactMessage } from '@/types';\n\ninterface DashboardStats {\n  totalUsers: number;\n  totalTemplates: number;\n  totalRequests: number;\n  pendingRequests: number;\n  totalSales: number;\n  customizations: number;\n}\n\nexport default function AdminDashboard() {\n  const { user, userData } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalUsers: 0,\n    totalTemplates: 0,\n    totalRequests: 0,\n    pendingRequests: 0,\n    totalSales: 0,\n    customizations: 0\n  });\n  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);\n  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);\n  const [recentUsers, setRecentUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [activeTab, setActiveTab] = useState('templates');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch dashboard stats\n        const dashboardStats = await getDashboardStats();\n        setStats(dashboardStats);\n\n        // Fetch custom requests\n        const requests = await getCustomRequests();\n        setCustomRequests(requests.slice(0, 5)); // Show only recent 5\n\n        // Fetch recent users\n        const users = await getAllUsers();\n        setRecentUsers(users.slice(0, 5)); // Show only recent 5\n\n        // Fetch contact messages\n        const messages = await getContactMessages();\n        setContactMessages(messages.slice(0, 10)); // Show only recent 10\n\n      } catch (error: any) {\n        console.error('Error fetching admin data:', error);\n        setError('Failed to load dashboard data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && userData?.role === 'admin') {\n      fetchData();\n\n      // Set up real-time listeners\n      const unsubscribeRequests = subscribeToCustomRequests((requests) => {\n        setCustomRequests(requests.slice(0, 5));\n      });\n\n      const unsubscribeMessages = subscribeToContactMessages((messages) => {\n        setContactMessages(messages.slice(0, 10));\n      });\n\n      return () => {\n        unsubscribeRequests();\n        unsubscribeMessages();\n      };\n    }\n  }, [user, userData]);\n\n  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {\n    try {\n      await updateCustomRequestStatus(requestId, status);\n      // The real-time listener will update the UI automatically\n    } catch (error: any) {\n      console.error('Error updating request status:', error);\n      setError('Failed to update request status');\n    }\n  };\n\n  const handleUpdateMessageStatus = async (messageId: string, status: ContactMessage['status']) => {\n    try {\n      await updateContactMessageStatus(messageId, status);\n      // The real-time listener will update the UI automatically\n    } catch (error: any) {\n      console.error('Error updating message status:', error);\n      setError('Failed to update message status');\n    }\n  };\n\n  if (!user || userData?.role !== 'admin') {\n    return (\n      <div className=\"container mx-auto px-4 py-20 text-center\">\n        <h1 className=\"text-2xl font-bold mb-4\">Access Denied</h1>\n        <p className=\"text-gray-600 mb-4\">You need admin privileges to access this page.</p>\n        <Button asChild>\n          <Link href=\"/dashboard\">Go to Dashboard</Link>\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                Admin Dashboard\n              </h1>\n              <p className=\"text-gray-600\">\n                Manage your marketplace and monitor performance\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Button asChild>\n                <Link href=\"/admin/add-template\">\n                  <Plus className=\"mr-2 h-4 w-4\" />\n                  Add Template\n                </Link>\n              </Button>\n              <Button asChild variant=\"outline\">\n                <Link href=\"/admin/setup\">\n                  <Download className=\"mr-2 h-4 w-4\" />\n                  Setup Sample Data\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n\n      {/* Loading State */}\n      {loading && (\n        <div className=\"flex justify-center items-center py-12\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Loading dashboard data...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"mb-8 p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <p className=\"text-red-800\">{error}</p>\n        </div>\n      )}\n\n        {/* Stats Cards */}\n        {!loading && (\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n            {/* Templates Card */}\n            <Card className=\"bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-blue-100 text-sm font-medium mb-1\">Templates</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalTemplates}</p>\n                    <p className=\"text-blue-100 text-xs\">Available templates</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <FileText className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Contact Requests Card */}\n            <Card className=\"bg-gradient-to-r from-green-500 to-green-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-green-100 text-sm font-medium mb-1\">Contact Requests</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalRequests}</p>\n                    <p className=\"text-green-100 text-xs\">Customer inquiries</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <MessageSquare className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Total Sales Card */}\n            <Card className=\"bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-purple-100 text-sm font-medium mb-1\">Total Sales</p>\n                    <p className=\"text-3xl font-bold\">{stats.pendingRequests}</p>\n                    <p className=\"text-purple-100 text-xs\">No revenue</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <DollarSign className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Customizations Card */}\n            <Card className=\"bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-orange-100 text-sm font-medium mb-1\">Customizations</p>\n                    <p className=\"text-3xl font-bold\">{stats.customizations}</p>\n                    <p className=\"text-orange-100 text-xs\">Total customizations</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <Palette className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Users Card */}\n            <Card className=\"bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-cyan-100 text-sm font-medium mb-1\">Users</p>\n                    <p className=\"text-3xl font-bold\">{stats.totalUsers}</p>\n                    <p className=\"text-cyan-100 text-xs\">Site visitors</p>\n                  </div>\n                  <div className=\"p-3 bg-white/20 rounded-lg\">\n                    <Users className=\"h-8 w-8 text-white\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Navigation Tabs */}\n        <div className=\"mb-8\">\n          <div className=\"border-b border-gray-200\">\n            <nav className=\"-mb-px flex space-x-8\">\n              <button\n                onClick={() => setActiveTab('templates')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'templates'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Templates Management\n              </button>\n              <button\n                onClick={() => setActiveTab('requests')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'requests'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Contact Requests\n              </button>\n              <button\n                onClick={() => setActiveTab('purchases')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'purchases'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Purchase Requests\n              </button>\n              <button\n                onClick={() => setActiveTab('customizations')}\n                className={`border-b-2 py-2 px-1 text-sm font-medium ${\n                  activeTab === 'customizations'\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                Customizations\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {!loading && (\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {/* Content based on active tab */}\n            <div className=\"lg:col-span-2\">\n              {activeTab === 'templates' && (\n                <TemplatesTab onRefresh={() => {\n                  // Refresh dashboard stats if needed\n                  console.log('Templates updated');\n                }} />\n              )}\n\n              {activeTab === 'requests' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Contact Requests</CardTitle>\n                    <CardDescription>\n                      Manage customer inquiries and requests\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {contactMessages.filter(msg => msg.type === 'contact').length > 0 ? (\n                        contactMessages.filter(msg => msg.type === 'contact').map((message) => (\n                          <div key={message.id} className=\"p-4 border rounded-lg\">\n                            <div className=\"flex items-start justify-between\">\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center gap-2 mb-2\">\n                                  <h4 className=\"font-medium text-gray-900\">{message.userName}</h4>\n                                  <Badge variant=\"secondary\" className=\"text-xs\">\n                                    Contact Inquiry\n                                  </Badge>\n                                </div>\n                                <p className=\"text-sm text-gray-600\">{message.userEmail}</p>\n                                {message.userPhone && (\n                                  <p className=\"text-sm text-gray-600\">{message.userPhone}</p>\n                                )}\n                                <p className=\"text-sm font-medium text-gray-800 mt-1\">{message.subject}</p>\n                                <p className=\"text-sm text-gray-500 mt-2 line-clamp-2\">{message.message}</p>\n                                {message.templateTitle && (\n                                  <p className=\"text-xs text-blue-600 mt-1\">Template: {message.templateTitle}</p>\n                                )}\n                                <p className=\"text-xs text-gray-400 mt-2\">\n                                  {new Date(message.createdAt.seconds ? message.createdAt.seconds * 1000 : message.createdAt).toLocaleDateString()}\n                                </p>\n                              </div>\n                              <div className=\"flex flex-col gap-2 ml-4\">\n                                <Badge variant={\n                                  message.status === 'pending' ? 'outline' :\n                                  message.status === 'responded' ? 'secondary' : 'default'\n                                }>\n                                  {message.status}\n                                </Badge>\n                                <Button\n                                  size=\"sm\"\n                                  variant=\"outline\"\n                                  onClick={() => handleUpdateMessageStatus(message.id, 'responded')}\n                                  disabled={message.status !== 'pending'}\n                                >\n                                  {message.status === 'pending' ? 'Respond' : 'Responded'}\n                                </Button>\n                              </div>\n                            </div>\n                          </div>\n                        ))\n                      ) : (\n                        <div className=\"text-center py-8\">\n                          <MessageSquare className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No contact requests</h3>\n                          <p className=\"text-gray-600\">\n                            Contact requests from users will appear here.\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {activeTab === 'purchases' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Purchase Requests</CardTitle>\n                    <CardDescription>\n                      Manage template purchase requests from customers\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {contactMessages.filter(msg => msg.type === 'purchase-request').length > 0 ? (\n                        contactMessages.filter(msg => msg.type === 'purchase-request').map((message) => (\n                          <div key={message.id} className=\"p-4 border rounded-lg\">\n                            <div className=\"flex items-start justify-between\">\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center gap-2 mb-2\">\n                                  <h4 className=\"font-medium text-gray-900\">{message.userName}</h4>\n                                  <Badge variant=\"default\" className=\"text-xs\">\n                                    Purchase Request\n                                  </Badge>\n                                </div>\n                                <p className=\"text-sm text-gray-600\">{message.userEmail}</p>\n                                {message.userPhone && (\n                                  <p className=\"text-sm text-gray-600\">{message.userPhone}</p>\n                                )}\n                                <p className=\"text-sm font-medium text-gray-800 mt-1\">{message.subject}</p>\n                                <p className=\"text-sm text-gray-500 mt-2 line-clamp-2\">{message.message}</p>\n                                {message.templateTitle && (\n                                  <p className=\"text-xs text-blue-600 mt-1\">Template: {message.templateTitle}</p>\n                                )}\n                                <p className=\"text-xs text-gray-400 mt-2\">\n                                  {new Date(message.createdAt.seconds ? message.createdAt.seconds * 1000 : message.createdAt).toLocaleDateString()}\n                                </p>\n                              </div>\n                              <div className=\"flex flex-col gap-2 ml-4\">\n                                <Badge variant={\n                                  message.status === 'pending' ? 'outline' :\n                                  message.status === 'responded' ? 'secondary' : 'default'\n                                }>\n                                  {message.status}\n                                </Badge>\n                                <Button\n                                  size=\"sm\"\n                                  variant=\"outline\"\n                                  onClick={() => handleUpdateMessageStatus(message.id, 'responded')}\n                                  disabled={message.status !== 'pending'}\n                                >\n                                  {message.status === 'pending' ? 'Respond' : 'Responded'}\n                                </Button>\n                              </div>\n                            </div>\n                          </div>\n                        ))\n                      ) : (\n                        <div className=\"text-center py-8\">\n                          <ShoppingCart className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No purchase requests</h3>\n                          <p className=\"text-gray-600\">\n                            Purchase requests from customers will appear here.\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              {activeTab === 'customizations' && (\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Customization Requests</CardTitle>\n                    <CardDescription>\n                      Manage custom template requests\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"p-4 border rounded-lg\">\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <h4 className=\"font-medium text-gray-900\">Custom Dashboard</h4>\n                            <p className=\"text-sm text-gray-600\">Client: <EMAIL></p>\n                            <p className=\"text-sm text-gray-500 mt-2\">Need a custom admin dashboard with specific features...</p>\n                          </div>\n                          <Badge variant=\"outline\">Pending</Badge>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <div>\n              <Card>\n                <CardHeader>\n                  <CardTitle>Quick Actions</CardTitle>\n                  <CardDescription>\n                    Common admin tasks\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Button\n                    onClick={() => setActiveTab('templates')}\n                    variant={activeTab === 'templates' ? 'default' : 'outline'}\n                    className=\"w-full justify-start\"\n                  >\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Templates\n                  </Button>\n\n                  <Button\n                    onClick={() => setActiveTab('requests')}\n                    variant={activeTab === 'requests' ? 'default' : 'outline'}\n                    className=\"w-full justify-start\"\n                  >\n                    <MessageSquare className=\"mr-2 h-4 w-4\" />\n                    Contact Requests\n                  </Button>\n\n                  <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                    <Link href=\"/admin/custom-requests\">\n                      <Palette className=\"mr-2 h-4 w-4\" />\n                      Customize Requests\n                    </Link>\n                  </Button>\n\n                  <Button asChild variant=\"outline\" className=\"w-full justify-start\">\n                    <Link href=\"/contact\">\n                      <Phone className=\"mr-2 h-4 w-4\" />\n                      Contact Page\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n\n              {/* System Status */}\n              <Card className=\"mt-6\">\n                <CardHeader>\n                  <CardTitle>System Status</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Firebase</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Connected</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Database className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Firestore</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <Users className=\"w-4 h-4 mr-2 text-green-600\" />\n                        <span className=\"text-sm text-gray-600\">Authentication</span>\n                      </div>\n                      <Badge variant=\"default\" className=\"bg-green-500\">Working</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center\">\n                        <FileText className=\"w-4 h-4 mr-2 text-gray-600\" />\n                        <span className=\"text-sm text-gray-600\">Total Templates</span>\n                      </div>\n                      <span className=\"text-sm text-gray-900\">{stats.totalTemplates}</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AAxBA;;;;;;;;;;;AA6Ce,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,YAAY;QACZ,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,gBAAgB;IAClB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,WAAW;gBAEX,wBAAwB;gBACxB,MAAM,iBAAiB,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBAC7C,SAAS;gBAET,wBAAwB;gBACxB,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;gBACvC,kBAAkB,SAAS,KAAK,CAAC,GAAG,KAAK,qBAAqB;gBAE9D,qBAAqB;gBACrB,MAAM,QAAQ,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;gBAC9B,eAAe,MAAM,KAAK,CAAC,GAAG,KAAK,qBAAqB;gBAExD,yBAAyB;gBACzB,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;gBACxC,mBAAmB,SAAS,KAAK,CAAC,GAAG,MAAM,sBAAsB;YAEnE,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,UAAU,SAAS,SAAS;YACtC;YAEA,6BAA6B;YAC7B,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,CAAC;gBACrD,kBAAkB,SAAS,KAAK,CAAC,GAAG;YACtC;YAEA,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,CAAC;gBACtD,mBAAmB,SAAS,KAAK,CAAC,GAAG;YACvC;YAEA,OAAO;gBACL;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,4BAA4B,OAAO,WAAmB;QAC1D,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;QAC3C,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX;IACF;IAEA,MAAM,4BAA4B,OAAO,WAAmB;QAC1D,IAAI;YACF,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,WAAW;QAC5C,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS;QACX;IACF;IAEA,IAAI,CAAC,QAAQ,UAAU,SAAS,SAAS;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,OAAO;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAa;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;kDACtB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;8DACT,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShD,yBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;gBAMlC,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;gBAK9B,CAAC,yBACA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,cAAc;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA0C;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,aAAa;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,eAAe;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,cAAc;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO3B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAsB,MAAM,UAAU;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,kCACA,8EACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,mBACV,kCACA,8EACJ;8CACH;;;;;;;;;;;;;;;;;;;;;;gBAON,CAAC,yBACA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,6BACb,8OAAC,2IAAA,CAAA,UAAY;oCAAC,WAAW;wCACvB,oCAAoC;wCACpC,QAAQ,GAAG,CAAC;oCACd;;;;;;gCAGD,cAAc,4BACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,WAAW,MAAM,GAAG,IAC9D,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,WAAW,GAAG,CAAC,CAAC,wBACzD,8OAAC;wDAAqB,WAAU;kEAC9B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAA6B,QAAQ,QAAQ;;;;;;8FAC3D,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAY,WAAU;8FAAU;;;;;;;;;;;;sFAIjD,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,SAAS;;;;;;wEACtD,QAAQ,SAAS,kBAChB,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,SAAS;;;;;;sFAEzD,8OAAC;4EAAE,WAAU;sFAA0C,QAAQ,OAAO;;;;;;sFACtE,8OAAC;4EAAE,WAAU;sFAA2C,QAAQ,OAAO;;;;;;wEACtE,QAAQ,aAAa,kBACpB,8OAAC;4EAAE,WAAU;;gFAA6B;gFAAW,QAAQ,aAAa;;;;;;;sFAE5E,8OAAC;4EAAE,WAAU;sFACV,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,OAAO,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8EAGlH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SACL,QAAQ,MAAM,KAAK,YAAY,YAC/B,QAAQ,MAAM,KAAK,cAAc,cAAc;sFAE9C,QAAQ,MAAM;;;;;;sFAEjB,8OAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,0BAA0B,QAAQ,EAAE,EAAE;4EACrD,UAAU,QAAQ,MAAM,KAAK;sFAE5B,QAAQ,MAAM,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;uDAnC1C,QAAQ,EAAE;;;;8EA0CtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAUxC,cAAc,6BACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,oBAAoB,MAAM,GAAG,IACvE,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,oBAAoB,GAAG,CAAC,CAAC,wBAClE,8OAAC;wDAAqB,WAAU;kEAC9B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;8FAA6B,QAAQ,QAAQ;;;;;;8FAC3D,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;8FAAU;;;;;;;;;;;;sFAI/C,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,SAAS;;;;;;wEACtD,QAAQ,SAAS,kBAChB,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,SAAS;;;;;;sFAEzD,8OAAC;4EAAE,WAAU;sFAA0C,QAAQ,OAAO;;;;;;sFACtE,8OAAC;4EAAE,WAAU;sFAA2C,QAAQ,OAAO;;;;;;wEACtE,QAAQ,aAAa,kBACpB,8OAAC;4EAAE,WAAU;;gFAA6B;gFAAW,QAAQ,aAAa;;;;;;;sFAE5E,8OAAC;4EAAE,WAAU;sFACV,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,GAAG,QAAQ,SAAS,CAAC,OAAO,GAAG,OAAO,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8EAGlH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SACL,QAAQ,MAAM,KAAK,YAAY,YAC/B,QAAQ,MAAM,KAAK,cAAc,cAAc;sFAE9C,QAAQ,MAAM;;;;;;sFAEjB,8OAAC,kIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,0BAA0B,QAAQ,EAAE,EAAE;4EACrD,UAAU,QAAQ,MAAM,KAAK;sFAE5B,QAAQ,MAAM,KAAK,YAAY,YAAY;;;;;;;;;;;;;;;;;;uDAnC1C,QAAQ,EAAE;;;;8EA0CtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAUxC,cAAc,kCACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;;;;;;0EAE5C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvC,8OAAC;;8CACC,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAS,cAAc,cAAc,YAAY;oDACjD,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIvC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa;oDAC5B,SAAS,cAAc,aAAa,YAAY;oDAChD,WAAU;;sEAEV,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAI5C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;;0EACT,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;8DAKxC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAC,SAAQ;oDAAU,WAAU;8DAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;;0EACT,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1C,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAe;;;;;;;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAE1C,8OAAC;gEAAK,WAAU;0EAAyB,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnF", "debugId": null}}]}