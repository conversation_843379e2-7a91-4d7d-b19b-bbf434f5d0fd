(()=>{var e={};e.id=305,e.ids=[305],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11903:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(60687),a=s(43210),i=s(63213),n=s(44493),c=s(29523),d=s(96834),l=s(34729),o=s(54300),u=s(91821),x=s(48730);let m=(0,s(62688).A)("circle-play",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var p=s(5336),h=s(35071),g=s(10022),v=s(58869),f=s(40228),b=s(23928),j=s(58887),y=s(41862),N=s(85814),w=s.n(N),A=s(55568);function q(){let{user:e,userData:t}=(0,i.A)(),[s,N]=(0,a.useState)([]),[q,k]=(0,a.useState)(!0),[M,P]=(0,a.useState)(""),[C,D]=(0,a.useState)(null),[z,G]=(0,a.useState)(""),[_,E]=(0,a.useState)(!1),R=async(e,t)=>{try{E(!0),await (0,A.gW)(e,t,z),N(s=>s.map(s=>s.id===e?{...s,status:t,adminNotes:z,updatedAt:new Date}:s)),D(null),G("")}catch(e){console.error("Error updating request status:",e),P("Failed to update request status")}finally{E(!1)}};if(!e||t?.role!=="admin")return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You need admin privileges to access this page."}),(0,r.jsx)(c.$,{asChild:!0,children:(0,r.jsx)(w(),{href:"/dashboard",children:"Go to Dashboard"})})]});let S=e=>{switch(e){case"pending":default:return(0,r.jsx)(x.A,{className:"h-4 w-4"});case"in-progress":return(0,r.jsx)(m,{className:"h-4 w-4"});case"completed":return(0,r.jsx)(p.A,{className:"h-4 w-4"});case"cancelled":return(0,r.jsx)(h.A,{className:"h-4 w-4"})}},Z=e=>{switch(e){case"pending":default:return"outline";case"in-progress":return"secondary";case"completed":return"default";case"cancelled":return"destructive"}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Custom Requests Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage and respond to custom design requests from users"})]}),q&&(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading custom requests..."})]})}),M&&(0,r.jsx)(u.Fc,{variant:"destructive",className:"mb-6",children:(0,r.jsx)(u.TN,{children:M})}),!q&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-yellow-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,r.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"pending"===e.status).length})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m,{className:"h-5 w-5 text-blue-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Progress"}),(0,r.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"in-progress"===e.status).length})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-xl font-bold",children:s.filter(e=>"completed"===e.status).length})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total"}),(0,r.jsx)("p",{className:"text-xl font-bold",children:s.length})]})]})})})]}),!q&&(0,r.jsx)("div",{className:"space-y-4",children:s.length>0?s.map(e=>(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,r.jsxs)(d.E,{variant:Z(e.status),className:"flex items-center space-x-1",children:[S(e.status),(0,r.jsx)("span",{children:e.status})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.userEmail})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.category})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:new Date(e.createdAt).toLocaleDateString()})]})]}),e.budget&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mb-3",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Budget: $",e.budget]})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-4",children:e.description}),e.adminNotes&&(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-blue-800",children:"Admin Notes"})]}),(0,r.jsx)("p",{className:"text-sm text-blue-700",children:e.adminNotes})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2 ml-4",children:["pending"===e.status&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(c.$,{size:"sm",onClick:()=>D(e),children:"Manage"})}),"in-progress"===e.status&&(0,r.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>D(e),children:"Update"})]})]})})},e.id)):(0,r.jsx)(n.Zp,{children:(0,r.jsxs)(n.Wu,{className:"p-12 text-center",children:[(0,r.jsx)(g.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Custom Requests"}),(0,r.jsx)("p",{className:"text-gray-600",children:"No custom design requests have been submitted yet."})]})})}),C&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsxs)(n.Zp,{className:"w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{children:["Manage Request: ",C.title]}),(0,r.jsx)(n.BT,{children:"Update the status and add admin notes for this request"})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"adminNotes",children:"Admin Notes"}),(0,r.jsx)(l.T,{id:"adminNotes",placeholder:"Add notes about this request...",value:z,onChange:e=>G(e.target.value),rows:4})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:["pending"===C.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(c.$,{onClick:()=>R(C.id,"in-progress"),disabled:_,children:[_&&(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Accept & Start"]}),(0,r.jsxs)(c.$,{variant:"destructive",onClick:()=>R(C.id,"cancelled"),disabled:_,children:[_&&(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Decline"]})]}),"in-progress"===C.status&&(0,r.jsxs)(c.$,{onClick:()=>R(C.id,"completed"),disabled:_,children:[_&&(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Mark Complete"]}),(0,r.jsx)(c.$,{variant:"outline",onClick:()=>{D(null),G("")},children:"Cancel"})]})]})]})})]})}},17937:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>l});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),c=s(30893),d={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);s.d(t,d);let l={children:["",{children:["admin",{children:["custom-requests",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74951)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/custom-requests/page",pathname:"/admin/custom-requests",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(60687),a=s(43210),i=s(14163),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var c=s(4780);function d({className:e,...t}){return(0,r.jsx)(n,{"data-slot":"label",className:(0,c.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55568:(e,t,s)=>{"use strict";s.d(t,{CF:()=>d,CV:()=>u,FQ:()=>m,KV:()=>n,SZ:()=>p,Tb:()=>x,Xm:()=>o,gW:()=>c,hx:()=>i,wx:()=>l});var r=s(75535),a=s(33784);let i=async e=>{try{return(await (0,r.gS)((0,r.rJ)(a.db,"customRequests"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating custom request:",e),e}},n=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"customRequests"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching custom requests:",e),e}},c=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,r.mZ)((0,r.H9)(a.db,"customRequests",e),i)}catch(e){throw console.error("Error updating custom request:",e),e}},d=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"users"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching users:",e),e}},l=async()=>{try{let[e,t,s]=await Promise.all([(0,r.GG)((0,r.rJ)(a.db,"users")),(0,r.GG)((0,r.rJ)(a.db,"templates")),(0,r.GG)((0,r.rJ)(a.db,"customRequests"))]),i=e.size,n=t.size,c=s.size,d=s.docs.filter(e=>"pending"===e.data().status).length;return{totalUsers:i,totalTemplates:n,totalRequests:c,pendingRequests:d,totalSales:0,customizations:0}}catch(e){throw console.error("Error fetching dashboard stats:",e),e}},o=e=>{let t=(0,r.P)((0,r.rJ)(a.db,"customRequests"),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})},u=async e=>{try{return(await (0,r.gS)((0,r.rJ)(a.db,"contactMessages"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating contact message:",e),e}},x=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"contactMessages"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching contact messages:",e),e}},m=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,r.mZ)((0,r.H9)(a.db,"contactMessages",e),i)}catch(e){throw console.error("Error updating contact message:",e),e}},p=e=>{let t=(0,r.P)((0,r.rJ)(a.db,"contactMessages"),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})}},58887:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},61915:(e,t,s)=>{Promise.resolve().then(s.bind(s,11903))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74951:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/admin/custom-requests/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>c,TN:()=>d});var r=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...s})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},96259:(e,t,s)=>{Promise.resolve().then(s.bind(s,74951))},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...i}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(c({variant:t}),e),...i})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,415,658,489],()=>s(17937));module.exports=r})();