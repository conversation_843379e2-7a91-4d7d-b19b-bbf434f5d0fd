(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6836:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(60687);s(43210);var a=s(63213),i=s(29523),n=s(44493),l=s(96834),d=s(5336),o=s(48730),c=s(35071),m=s(71057),x=s(23928),u=s(40228),p=s(13861),h=s(31158),g=s(85814),f=s.n(g);let j=[{id:"ORD-001",templateId:"1",templateName:"Modern Dashboard Pro",templateImage:"/api/placeholder/100/80",amount:49,status:"completed",orderDate:"2024-01-15",downloadCount:3,maxDownloads:5},{id:"ORD-002",templateId:"2",templateName:"E-commerce Store Complete",templateImage:"/api/placeholder/100/80",amount:79,status:"confirmed",orderDate:"2024-01-10",downloadCount:1,maxDownloads:3},{id:"ORD-003",templateId:"3",templateName:"Landing Page Pro",templateImage:"/api/placeholder/100/80",amount:39,status:"pending",orderDate:"2024-01-08",downloadCount:0,maxDownloads:5},{id:"ORD-004",templateId:"4",templateName:"Creative Portfolio",templateImage:"/api/placeholder/100/80",amount:29,status:"declined",orderDate:"2024-01-05",downloadCount:0,maxDownloads:3}],v=e=>{switch(e){case"completed":return(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-500"});case"confirmed":return(0,r.jsx)(d.A,{className:"h-5 w-5 text-blue-500"});case"pending":return(0,r.jsx)(o.A,{className:"h-5 w-5 text-yellow-500"});case"declined":return(0,r.jsx)(c.A,{className:"h-5 w-5 text-red-500"});default:return(0,r.jsx)(o.A,{className:"h-5 w-5 text-gray-500"})}},y=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"confirmed":return"bg-blue-100 text-blue-800";case"pending":return"bg-yellow-100 text-yellow-800";case"declined":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};function b(){let{user:e}=(0,a.A)();if(!e)return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to view your orders"}),(0,r.jsx)(i.$,{asChild:!0,children:(0,r.jsx)(f(),{href:"/auth",children:"Sign In"})})]});let t=j.filter(e=>"completed"===e.status).length,s=j.filter(e=>"completed"===e.status||"confirmed"===e.status).reduce((e,t)=>e+t.amount,0);return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Orders"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your template purchases and downloads"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Orders"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.length})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(d.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Spent"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",s]})]}),(0,r.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-purple-600"})})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Order History"}),(0,r.jsx)(n.BT,{children:"All your template purchases and their current status"})]}),(0,r.jsx)(n.Wu,{children:0===j.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(m.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No orders yet"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Start browsing our premium templates to make your first purchase."}),(0,r.jsx)(i.$,{asChild:!0,children:(0,r.jsx)(f(),{href:"/templates",children:"Browse Templates"})})]}):(0,r.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,r.jsx)("div",{className:"border rounded-lg p-6 hover:shadow-md transition-shadow",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,r.jsx)("div",{className:"w-20 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0",children:(0,r.jsx)("img",{src:e.templateImage,alt:e.templateName,className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:e.templateName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Order #",e.id]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-semibold text-gray-900",children:["$",e.amount]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 flex items-center",children:[(0,r.jsx)(u.A,{className:"h-3 w-3 mr-1"}),new Date(e.orderDate).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[v(e.status),(0,r.jsx)(l.E,{className:y(e.status),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),("completed"===e.status||"confirmed"===e.status)&&(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:["Downloads: ",e.downloadCount,"/",e.maxDownloads]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.$,{size:"sm",variant:"outline",asChild:!0,children:(0,r.jsxs)(f(),{href:`/templates/${e.templateId}`,children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"View"]})}),"completed"===e.status&&(0,r.jsxs)(i.$,{size:"sm",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]})]})]})})},e.id))})})]}),(0,r.jsx)(n.Zp,{className:"mt-8",children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Need Help with Your Order?"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"If you have any questions about your orders or need assistance with downloads, we're here to help."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(i.$,{asChild:!0,variant:"outline",children:(0,r.jsx)(f(),{href:"/contact",children:"Contact Support"})}),(0,r.jsx)(i.$,{asChild:!0,variant:"outline",children:(0,r.jsx)(f(),{href:"/templates",children:"Browse More Templates"})})]})]})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11965:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx","default")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},26042:(e,t,s)=>{Promise.resolve().then(s.bind(s,11965))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},37366:e=>{"use strict";e.exports=require("dns")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66210:(e,t,s)=>{Promise.resolve().then(s.bind(s,6836))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...i}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}},97555:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11965)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/orders/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,415,658,489],()=>s(97555));module.exports=r})();