(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},39125:(e,s,t)=>{Promise.resolve().then(t.bind(t,80559))},43853:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),d=t(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(s,l);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80559)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function d({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...s})}function l({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...s})}function c({className:e,...s}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...s})}},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52693:(e,s,t)=>{Promise.resolve().then(t.bind(t,58061))},55511:e=>{"use strict";e.exports=require("crypto")},58061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(60687);t(43210);var a=t(63213),i=t(44493),n=t(29523),d=t(96834),l=t(71057),c=t(31158),o=t(64398),x=t(48730),m=t(13861),u=t(96474),p=t(85814),h=t.n(p);let j={totalPurchases:12,totalDownloads:45,favoriteTemplates:8,pendingOrders:2},g=[{id:"1",templateName:"Modern Dashboard Pro",purchaseDate:"2024-01-15",amount:49,status:"completed"},{id:"2",templateName:"E-commerce Store Template",purchaseDate:"2024-01-10",amount:79,status:"completed"},{id:"3",templateName:"Landing Page Bundle",purchaseDate:"2024-01-08",amount:39,status:"pending"}];function v(){let{user:e,userData:s}=(0,a.A)();return e?(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,r.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,r.jsxs)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:["Welcome back, ",s?.displayName||e.email?.split("@")[0],"!"]}),(0,r.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Manage your templates, orders, and account settings from your dashboard."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Purchases"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.totalPurchases})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(l.A,{className:"h-6 w-6 text-blue-600"})})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Downloads"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.totalDownloads})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-green-600"})})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Favorites"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.favoriteTemplates})]}),(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-yellow-600"})})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Orders"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:j.pendingOrders})]}),(0,r.jsx)("div",{className:"p-3 bg-orange-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"h-6 w-6 text-orange-600"})})]})})})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Recent Purchases"}),(0,r.jsx)(i.BT,{children:"Your latest template purchases and downloads"})]}),(0,r.jsxs)(i.Wu,{children:[(0,r.jsx)("div",{className:"space-y-4",children:g.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.templateName}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Purchased on ",new Date(e.purchaseDate).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(d.E,{variant:"completed"===e.status?"default":"secondary",children:e.status}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.amount]}),"completed"===e.status&&(0,r.jsxs)(n.$,{size:"sm",variant:"outline",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Download"]})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(h(),{href:"/orders",children:"View All Orders"})})})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Quick Actions"}),(0,r.jsx)(i.BT,{children:"Common tasks and shortcuts"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsx)(n.$,{asChild:!0,className:"w-full justify-start",children:(0,r.jsxs)(h(),{href:"/templates",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Browse Templates"]})}),s?.role==="admin"&&(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)(h(),{href:"/admin/setup",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Setup Sample Data"]})}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)(h(),{href:"/orders",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"View Orders"]})}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full justify-start",children:(0,r.jsxs)(h(),{href:"/favorites",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"My Favorites"]})})]})]}),(0,r.jsxs)(i.Zp,{className:"mt-6",children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Account Information"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:e.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Account Type"}),(0,r.jsx)(d.E,{variant:s?.role==="admin"?"default":"secondary",children:s?.role||"user"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Member Since"}),(0,r.jsx)("p",{className:"text-sm text-gray-900",children:s?.createdAt?new Date(s.createdAt).toLocaleDateString():"N/A"})]})]})})]})]})]})]}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Please sign in to access your dashboard"}),(0,r.jsx)(n.$,{asChild:!0,children:(0,r.jsx)(h(),{href:"/auth",children:"Sign In"})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/dashboard/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,asChild:t=!1,...i}){let l=t?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),e),...i})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,415,658,489],()=>t(43853));module.exports=r})();