(()=>{var e={};e.id=372,e.ids=[372],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var r=s(60687);s(43210);var a=s(97822),i=s(78272),n=s(13964),d=s(3589),o=s(4780);function l({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,r.jsx)(x,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(d.A,{className:"size-4"})})}function h({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26208:(e,t,s)=>{Promise.resolve().then(s.bind(s,74218))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28488:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx","default")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},37366:e=>{"use strict";e.exports=require("dns")},41312:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>o});var r=s(60687),a=s(43210),i=s(14163),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=s(4780);function o({className:e,...t}){return(0,r.jsx)(n,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55568:(e,t,s)=>{"use strict";s.d(t,{CF:()=>o,CV:()=>u,FQ:()=>m,KV:()=>n,SZ:()=>x,Tb:()=>p,Xm:()=>c,gW:()=>d,hx:()=>i,wx:()=>l});var r=s(75535),a=s(33784);let i=async e=>{try{return(await (0,r.gS)((0,r.rJ)(a.db,"customRequests"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating custom request:",e),e}},n=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"customRequests"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching custom requests:",e),e}},d=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,r.mZ)((0,r.H9)(a.db,"customRequests",e),i)}catch(e){throw console.error("Error updating custom request:",e),e}},o=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"users"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching users:",e),e}},l=async()=>{try{let[e,t,s]=await Promise.all([(0,r.GG)((0,r.rJ)(a.db,"users")),(0,r.GG)((0,r.rJ)(a.db,"templates")),(0,r.GG)((0,r.rJ)(a.db,"customRequests"))]),i=e.size,n=t.size,d=s.size,o=s.docs.filter(e=>"pending"===e.data().status).length;return{totalUsers:i,totalTemplates:n,totalRequests:d,pendingRequests:o,totalSales:0,customizations:0}}catch(e){throw console.error("Error fetching dashboard stats:",e),e}},c=e=>{let t=(0,r.P)((0,r.rJ)(a.db,"customRequests"),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})},u=async e=>{try{return(await (0,r.gS)((0,r.rJ)(a.db,"contactMessages"),{...e,createdAt:new Date,updatedAt:new Date})).id}catch(e){throw console.error("Error creating contact message:",e),e}},p=async()=>{try{let e=(0,r.P)((0,r.rJ)(a.db,"contactMessages"),(0,r.My)("createdAt","desc"));return(await (0,r.GG)(e)).docs.map(e=>({id:e.id,...e.data()}))}catch(e){throw console.error("Error fetching contact messages:",e),e}},m=async(e,t,s)=>{try{let i={status:t,updatedAt:new Date};s&&(i.adminNotes=s),await (0,r.mZ)((0,r.H9)(a.db,"contactMessages",e),i)}catch(e){throw console.error("Error updating contact message:",e),e}},x=e=>{let t=(0,r.P)((0,r.rJ)(a.db,"contactMessages"),(0,r.My)("createdAt","desc"));return(0,r.aQ)(t,t=>{e(t.docs.map(e=>({id:e.id,...e.data()})))})}},62656:(e,t,s)=>{Promise.resolve().then(s.bind(s,28488))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74218:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var r=s(60687),a=s(43210),i=s(63213),n=s(29523),d=s(89667),o=s(54300),l=s(34729),c=s(44493),u=s(15079),p=s(91821),m=s(96834),x=s(98971),h=s(62688);let g=(0,h.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var v=s(41312);let f=(0,h.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var b=s(5336),j=s(41862),y=s(85814),w=s.n(y),N=s(55568);let q=["Dashboard","E-commerce","Landing Page","Portfolio","Corporate","Mobile App","Blog/CMS","Other"],k=[{icon:x.A,title:"Custom Design",description:"Tailored specifically to your brand and requirements"},{icon:g,title:"Fast Delivery",description:"Most projects completed within 5-10 business days"},{icon:v.A,title:"Expert Team",description:"Experienced designers and developers working on your project"},{icon:f,title:"Full Documentation",description:"Complete setup guide and documentation included"}];function A(){let{user:e,userData:t}=(0,i.A)(),[s,x]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[v,f]=(0,a.useState)(""),[y,A]=(0,a.useState)({title:"",description:"",category:"",budget:"",deadline:"",features:"",inspiration:""}),P=e=>{A(t=>({...t,[e.target.name]:e.target.value})),f("")},z=async t=>{if(t.preventDefault(),x(!0),f(""),!y.title||!y.description||!y.category){f("Please fill in all required fields"),x(!1);return}try{await (0,N.hx)({userId:e.uid,userEmail:e.email,title:y.title,description:y.description,category:y.category,budget:y.budget?parseFloat(y.budget.replace(/[^0-9.-]+/g,"")):void 0,deadline:y.deadline?new Date(y.deadline):void 0,status:"pending"}),g(!0)}catch(e){f(e.message||"Failed to submit request")}finally{x(!1)}};return e?h?(0,r.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,r.jsx)(c.Zp,{className:"max-w-2xl mx-auto text-center",children:(0,r.jsxs)(c.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(b.A,{className:"h-16 w-16 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Request Submitted!"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Thank you for your custom design request. Our team will review it and get back to you within 24 hours."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(n.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(w(),{href:"/dashboard",children:"Go to Dashboard"})}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(w(),{href:"/templates",children:"Browse Templates"})})]})]})})}):(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:"Request Custom Design"}),(0,r.jsx)("p",{className:"text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto",children:"Can't find what you're looking for? Let our expert team create a custom template tailored to your specific needs."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6 lg:gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(c.Zp,{children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsx)(c.ZB,{children:"Project Details"}),(0,r.jsx)(c.BT,{children:"Tell us about your project requirements and we'll create something amazing for you."})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"title",children:"Project Title *"}),(0,r.jsx)(d.p,{id:"title",name:"title",placeholder:"e.g., Modern SaaS Dashboard",value:y.title,onChange:P,required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)(u.l6,{value:y.category,onValueChange:e=>{A(t=>({...t,category:e}))},children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select a category"})}),(0,r.jsx)(u.gC,{children:q.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"description",children:"Project Description *"}),(0,r.jsx)(l.T,{id:"description",name:"description",placeholder:"Describe your project in detail. What is the purpose? Who is the target audience? What features do you need?",value:y.description,onChange:P,rows:4,required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"features",children:"Required Features"}),(0,r.jsx)(l.T,{id:"features",name:"features",placeholder:"List specific features you need (e.g., user authentication, payment integration, responsive design, etc.)",value:y.features,onChange:P,rows:3})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"budget",children:"Budget Range (USD)"}),(0,r.jsx)(d.p,{id:"budget",name:"budget",placeholder:"e.g., $500 - $1000",value:y.budget,onChange:P})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"deadline",children:"Preferred Deadline"}),(0,r.jsx)(d.p,{id:"deadline",name:"deadline",type:"date",value:y.deadline,onChange:P})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"inspiration",children:"Inspiration/References"}),(0,r.jsx)(l.T,{id:"inspiration",name:"inspiration",placeholder:"Share any websites, designs, or references that inspire you. Include URLs if possible.",value:y.inspiration,onChange:P,rows:3})]}),v&&(0,r.jsx)(p.Fc,{variant:"destructive",children:(0,r.jsx)(p.TN,{children:v})}),(0,r.jsxs)(n.$,{type:"submit",className:"w-full",disabled:s,children:[s&&(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submit Request"]})]})})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Why Choose Custom Design?"})}),(0,r.jsx)(c.Wu,{className:"space-y-4",children:k.map((e,t)=>{let s=e.icon;return(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,r.jsx)(s,{className:"h-5 w-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},t)})})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Our Process"})}),(0,r.jsxs)(c.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Submit Request"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Fill out the form with your requirements"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Review & Quote"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"We'll review and send you a detailed quote"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Design & Develop"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Our team creates your custom template"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:"Delivery"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Receive your completed template with documentation"})]})]})]})]}),(0,r.jsxs)(c.Zp,{children:[(0,r.jsx)(c.aR,{children:(0,r.jsx)(c.ZB,{children:"Need Help?"})}),(0,r.jsxs)(c.Wu,{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Have questions about custom design? Our team is here to help."}),(0,r.jsx)(n.$,{asChild:!0,variant:"outline",className:"w-full",children:(0,r.jsx)(w(),{href:"/contact",children:"Contact Us"})})]})]})]})]})]}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-20 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Sign in Required"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Please sign in to submit a custom design request."}),(0,r.jsx)(n.$,{asChild:!0,children:(0,r.jsx)(w(),{href:"/auth",children:"Sign In"})})]})}},79551:e=>{"use strict";e.exports=require("url")},79975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l={children:["",{children:["custom-request",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28488)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/home/<USER>/Documents/augment-projects/kaleidonex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/Documents/augment-projects/kaleidonex/src/app/custom-request/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/custom-request/page",pathname:"/custom-request",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>d,TN:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...s}){return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...s})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...i}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),e),...i})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,415,658,818,489],()=>s(79975));module.exports=r})();